#!/usr/bin/env python3
"""
Test Script cho Common Streaming Factory

Script này test cách common factory cung cấp unified event flow
với endpoint-specific data injection cho từng loại API.

Usage:
    python test_common_streaming_factory.py
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
import httpx

# Import stream client
try:
    from src.server.shared.stream_client import RedisStreamClient
except ImportError:
    print("❌ Không thể import stream client. Đảm bảo đã cài đặt dependencies:")
    print("   pip install aioredis>=2.0.0")
    exit(1)


class CommonFactoryTester:
    """
    Class để test common streaming factory với unified event flow
    """
    
    def __init__(self, server_url: str = "http://localhost:8007/mcp"):
        self.server_url = server_url
        self.stream_client = RedisStreamClient()
        
    async def test_common_flow_api(
        self,
        tool_name: str,
        expected_provider_type: str,
        bearer_token: Optional[str] = None,
        stream_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Test một API endpoint với common streaming flow
        
        Args:
            tool_name: Tên tool (operation_id từ OpenAPI)
            expected_provider_type: Loại data provider expected
            bearer_token: Bearer token cho authentication
            stream_key: Stream key (tự generate nếu None)
            
        Returns:
            Dict chứa kết quả test
        """
        
        # Generate stream key nếu không có
        if not stream_key:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            stream_key = f"common_{tool_name}_{timestamp}_{unique_id}"
        
        print(f"🧪 Testing Common Flow API: {tool_name}")
        print(f"📡 Stream Key: {stream_key}")
        print(f"🎯 Expected Provider: {expected_provider_type}")
        print("-" * 70)
        
        # Tạo MCP request
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": {}
            }
        }
        
        # Headers với stream key và Bearer token
        headers = {
            "Content-Type": "application/json",
            "Stream-Key": stream_key
        }
        
        if bearer_token:
            headers["Authorization"] = f"Bearer {bearer_token}"
            print(f"🔑 Using Bearer token: {bearer_token[:30]}...")
        
        # Start polling task
        polling_task = asyncio.create_task(
            self._poll_common_flow_events(stream_key, expected_provider_type)
        )
        
        # Gọi tool
        tool_result = None
        tool_error = None
        
        try:
            print("🚀 Gọi API tool với common flow...")
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.server_url,
                    json=mcp_request,
                    headers=headers
                )
                
                if response.status_code == 200:
                    result_data = response.json()
                    if "result" in result_data:
                        tool_result = result_data["result"]
                        print("✅ API tool completed successfully!")
                    else:
                        tool_error = result_data.get("error", "Unknown error")
                        print(f"❌ API tool failed: {tool_error}")
                else:
                    tool_error = f"HTTP {response.status_code}: {response.text}"
                    print(f"❌ HTTP Error: {tool_error}")
                    
        except Exception as e:
            tool_error = str(e)
            print(f"❌ Exception: {tool_error}")
        
        # Wait for polling to complete
        try:
            await asyncio.wait_for(polling_task, timeout=20.0)
        except asyncio.TimeoutError:
            polling_task.cancel()
            print("⏰ Polling timeout")
        
        print("-" * 70)
        
        return {
            "stream_key": stream_key,
            "tool_name": tool_name,
            "expected_provider": expected_provider_type,
            "tool_result": tool_result,
            "tool_error": tool_error,
            "success": tool_result is not None
        }
    
    async def _poll_common_flow_events(self, stream_key: str, expected_provider: str):
        """Poll common flow events và analyze unified pattern"""
        print("📊 Bắt đầu polling common flow events...")
        
        events_received = []
        stages_seen = set()
        data_types_seen = set()
        
        # Expected common stages
        expected_stages = [
            "started", "preparing", "executing", "processing", 
            "data_ready", "finalizing", "completed"
        ]
        
        try:
            async for update in self.stream_client.poll_stream(stream_key, timeout=45.0):
                event = update.get("event", "unknown")
                event_type = update.get("event_type", "unknown")
                timestamp = update.get("timestamp", "")
                
                events_received.append({
                    "event": event,
                    "event_type": event_type,
                    "timestamp": timestamp,
                    "data": update
                })
                stages_seen.add(event_type)
                
                # Format timestamp
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = dt.strftime("%H:%M:%S.%f")[:-3]
                    except:
                        time_str = timestamp[:12]
                else:
                    time_str = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                
                # Display events với common flow analysis
                if event == "tool_chunk":
                    if event_type == "started":
                        endpoint_type = update.get("endpoint_type", "unknown")
                        operation_id = update.get("operation_id", "unknown")
                        
                        print(f"[{time_str}] 🏁 Common Flow Started")
                        print(f"                    🔧 Tool: {operation_id}")
                        print(f"                    📍 Type: {endpoint_type}")
                        print(f"                    🎯 Expected Provider: {expected_provider}")
                        
                        if expected_provider in endpoint_type:
                            print(f"                    ✅ Provider match!")
                        else:
                            print(f"                    ❌ Provider mismatch!")
                        
                    elif event_type == "progress":
                        progress = update.get("progress", 0)
                        message = update.get("message", "")
                        progress_bar = "█" * (progress // 5) + "░" * (20 - progress // 5)
                        print(f"[{time_str}] 📈 [{progress_bar}] {progress}% - {message}")
                        
                        # Check if this is a common stage
                        stage_indicator = ""
                        for stage in expected_stages:
                            if stage in message.lower():
                                stage_indicator = f" ({stage.upper()})"
                                break
                        print(f"                    🔄 Stage{stage_indicator}")
                        
                    elif event_type == "data":
                        data_type = update.get("data_type", "unknown")
                        message = update.get("message", "")
                        payload = update.get("payload")
                        
                        data_types_seen.add(data_type)
                        
                        print(f"[{time_str}] 📊 Data Chunk: {data_type}")
                        print(f"                    💬 {message}")
                        
                        if payload:
                            try:
                                if isinstance(payload, str):
                                    payload_data = json.loads(payload)
                                else:
                                    payload_data = payload
                                
                                # Show endpoint-specific data
                                endpoint_type = payload_data.get("endpoint_type", "")
                                if endpoint_type:
                                    print(f"                    🎯 Provider Type: {endpoint_type}")
                                
                                # Show stage-specific data
                                stage = payload_data.get("stage", "")
                                if stage:
                                    print(f"                    🔄 Stage: {stage}")
                                
                                print(f"                    📄 Data: {json.dumps(payload_data, ensure_ascii=False)}")
                            except:
                                print(f"                    📄 Data: {str(payload)[:100]}...")
                        
                    elif event_type == "completed":
                        print(f"[{time_str}] ✅ Common Flow completed successfully!")
                        
                        result = update.get("result")
                        if result:
                            try:
                                if isinstance(result, str) and len(result) > 200:
                                    result_preview = result[:200] + "..."
                                else:
                                    result_preview = str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
                                print(f"                    📄 Result preview: {result_preview}")
                            except:
                                print(f"                    📄 Result: <complex data>")
                        break
                        
                    elif event_type == "error":
                        error = update.get("error", "Unknown error")
                        print(f"[{time_str}] ❌ Common Flow failed: {error}")
                        break
                        
                    else:
                        print(f"[{time_str}] 🔄 Stage: {event_type}")
                        
        except Exception as e:
            print(f"❌ Lỗi polling common flow events: {e}")
        
        # Analysis summary
        print(f"\n📊 Common Flow Analysis:")
        print(f"   Total events: {len(events_received)}")
        print(f"   Stages seen: {sorted(stages_seen)}")
        print(f"   Data types: {sorted(data_types_seen)}")
        print(f"   Expected provider: {expected_provider}")
        
        # Check if all common stages were seen
        common_stages_found = [stage for stage in expected_stages if stage in stages_seen]
        print(f"   Common stages found: {len(common_stages_found)}/{len(expected_stages)}")
        
        if len(common_stages_found) >= 5:  # At least 5 stages
            print(f"   ✅ Complete common flow detected")
        else:
            print(f"   ❌ Incomplete common flow")
    
    async def run_tests(self):
        """Chạy tất cả tests cho Common Streaming Factory"""
        print("=" * 80)
        print("🧪 COMMON STREAMING FACTORY TEST")
        print("=" * 80)
        
        # Connect to Redis
        await self.stream_client.connect()
        
        if not self.stream_client.redis:
            print("❌ Không thể kết nối Redis. Đảm bảo Redis đang chạy:")
            print("   docker-compose up redis -d")
            return
        
        print("✅ Kết nối Redis thành công!")
        print()
        
        # Test Bearer token (optional)
        test_bearer_token = None
        
        # Test cases cho different data providers với common flow
        test_cases = [
            {
                "name": "Paginated List (Common Flow)",
                "tool": "get_user_data_media",
                "expected_provider": "paginated_list"
            },
            {
                "name": "Statistics (Common Flow)",
                "tool": "get_user_data_statistics", 
                "expected_provider": "statistics"
            },
            {
                "name": "Search (Common Flow)",
                "tool": "search_user_media",
                "expected_provider": "search"
            },
            {
                "name": "Single Item (Common Flow)",
                "tool": "get_user_media_by_id",
                "expected_provider": "default"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}/{len(test_cases)}: {test_case['name']}")
            
            try:
                result = await self.test_common_flow_api(
                    tool_name=test_case["tool"],
                    expected_provider_type=test_case["expected_provider"],
                    bearer_token=test_bearer_token
                )
                results.append(result)
                
                if result["success"]:
                    print("✅ Test PASSED")
                else:
                    print("❌ Test FAILED")
                    
            except Exception as e:
                print(f"❌ Test ERROR: {e}")
                results.append({
                    "tool_name": test_case["tool"],
                    "success": False,
                    "error": str(e)
                })
            
            print()
        
        # Summary
        print("=" * 80)
        print("📊 COMMON FACTORY TEST SUMMARY")
        print("=" * 80)
        
        passed = sum(1 for r in results if r.get("success", False))
        total = len(results)
        
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {passed/total*100:.1f}%" if total > 0 else "N/A")
        
        print("\n📋 Common Flow Test Results:")
        for result in results:
            tool = result.get("tool_name", "unknown")
            provider = result.get("expected_provider", "unknown")
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            print(f"   {tool} ({provider}): {status}")
        
        # Cleanup
        await self.stream_client.disconnect()


async def main():
    """Main function"""
    tester = CommonFactoryTester()
    await tester.run_tests()


if __name__ == "__main__":
    print("🚀 Starting Common Streaming Factory Test...")
    print("📋 Đảm bảo các services đang chạy:")
    print("   - Redis: docker-compose up redis -d")
    print("   - Data Module Server: python src/server/redai_system/data/data_module_server.py")
    print()
    print("🎯 Test sẽ verify:")
    print("   - Common event flow: started → preparing → executing → processing → data_ready → finalizing → completed")
    print("   - Endpoint-specific data injection cho từng stage")
    print("   - Different data providers với unified flow")
    print("   - tool_chunk events với consistent structure")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
