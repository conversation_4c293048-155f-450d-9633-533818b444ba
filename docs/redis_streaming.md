# Redis Tool Streaming

Tính năng streaming cho MCP tools sử dụng Redis để stream real-time progress và intermediate results trong khi tool đang execute.

## 🎯 Tổng quan

### Workflow
1. **Client gửi request** với `X-Stream-Id` header
2. **Tool execution bắt đầu**, stream progress lên Redis
3. **Client poll Redis** để lấy real-time updates
4. **Tool hoàn thành**, trả về kết quả cuối cùng qua MCP

### Lợi ích
- ✅ **Real-time feedback**: Client biết tool đang làm gì
- ✅ **Better UX**: Không phải chờ đợi mù quáng
- ✅ **Non-blocking**: Tool vẫn trả về kết quả bình thường
- ✅ **Scalable**: Redis handle concurrent streams
- ✅ **Optional**: Tools hoạt động bình thường nếu không có stream_id

## 🏗️ Kiến trúc

```mermaid
graph TD
    A[Client gọi tool với X-Stream-Id] --> B[MCP Server nhận request]
    B --> C[Streaming decorator wrap tool]
    C --> D[Stream 'started' lên Redis]
    D --> E[Tool execution...]
    E --> F[Stream progress updates]
    F --> G[Tool hoàn thành]
    G --> H[Stream 'completed' lên Redis]
    H --> I[Trả về kết quả cho client]
    
    F --> J[Redis Stream]
    D --> J
    H --> J
    
    K[Client polling Redis] --> J
```

## 🚀 Cài đặt

### 1. Dependencies
```bash
pip install aioredis>=2.0.0
```

### 2. Redis Server
```bash
# Chạy Redis với Docker
docker run -d -p 6379:6379 redis:7-alpine

# Hoặc thêm vào docker-compose.yml (đã có sẵn)
docker-compose up redis
```

### 3. Environment Variables
```bash
# Optional - Redis URL (default: redis://localhost:6379)
REDIS_URL=redis://localhost:6379

# Optional - Stream TTL in seconds (default: 3600)
REDIS_STREAM_TTL=3600
```

## 💻 Sử dụng

### Server Side - Tạo Streaming Tool

```python
from src.server.shared.streaming_tool_decorator import streaming_tool, stream_progress

@mcp.tool()
@streaming_tool(enable_progress=True, auto_progress=False)
async def my_streaming_tool(
    arg1: str,
    arg2: int,
    stream_id: Optional[str] = None  # Injected by decorator
) -> str:
    """Tool với streaming support"""
    
    # Manual progress streaming
    await stream_progress(stream_id, 25, "Bắt đầu xử lý...")
    
    # Do some work
    await asyncio.sleep(1)
    
    await stream_progress(stream_id, 75, "Gần hoàn thành...")
    
    # More work
    result = "Tool completed!"
    
    # Final result sẽ được stream tự động bởi decorator
    return result
```

### Client Side - Gọi Tool với Streaming

```python
import httpx
import uuid
from src.server.shared.stream_client import RedisStreamClient

async def call_streaming_tool():
    # Generate unique stream key
    stream_key = f"my_stream_{uuid.uuid4().hex[:8]}"

    # Tạo stream client
    stream_client = RedisStreamClient()

    # Start polling task
    polling_task = asyncio.create_task(
        poll_and_display_progress(stream_client, stream_key)
    )

    # Gọi MCP tool với Stream-Key header
    headers = {"Stream-Key": stream_key}  # Sử dụng Stream-Key header
    mcp_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "my_streaming_tool",
            "arguments": {"arg1": "test", "arg2": 42}
        }
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8009/mcp",
            json=mcp_request,
            headers=headers
        )
        
        # Kết quả cuối cùng từ MCP
        result = response.json()
        print(f"Final result: {result}")
    
    # Wait for polling to complete
    await polling_task
    await stream_client.disconnect()

async def poll_and_display_progress(stream_client, stream_id):
    """Poll và hiển thị progress"""
    async for update in stream_client.poll_stream(stream_id):
        status = update.get("status")
        progress = update.get("progress", 0)
        message = update.get("message", "")
        
        if status == "progress":
            print(f"Progress: {progress}% - {message}")
        elif status == "completed":
            print("✅ Tool completed!")
            break
        elif status == "error":
            print(f"❌ Error: {update.get('error')}")
            break
```

## 🔧 API Reference

### Streaming Decorator

```python
@streaming_tool(
    enable_progress: bool = True,           # Enable progress streaming
    progress_interval: float = 1.0,        # Auto progress interval
    auto_progress: bool = False,            # Auto progress based on time
    stream_key_header: str = "Stream-Key"   # Header name chứa stream key
)
```

### Header Configuration

Client gửi stream key qua header:
- **Primary**: `Stream-Key` (recommended)
- **Fallback**: `X-Stream-Id`, `Stream-Id`, `stream-key`
- **Custom**: Có thể config header name khác

```python
# Sử dụng Stream-Key header (default)
@streaming_tool()
async def my_tool(stream_id: Optional[str] = None):
    pass

# Sử dụng custom header
@streaming_tool(stream_key_header="Custom-Stream-Header")
async def custom_tool(stream_id: Optional[str] = None):
    pass
```

### Manual Progress Streaming

```python
await stream_progress(
    stream_id: Optional[str],          # Stream ID from decorator
    progress: int,                     # Progress 0-100
    message: str = "",                 # Progress message
    data: Optional[Dict] = None        # Additional data
)
```

### Stream Client

```python
client = RedisStreamClient(redis_url="redis://localhost:6379")

# Poll stream for updates
async for update in client.poll_stream(stream_id, timeout=30.0):
    # Handle update
    pass

# Get stream history
history = await client.get_stream_history(stream_id)

# Wait for completion
result = await client.wait_for_completion(stream_id, timeout=300.0)
```

## 📊 Stream Data Format với tool_chunk Events

### tool_chunk Event Structure
```json
{
  "id": "1640995200000-0",
  "stream_id": "my_stream_abc123",
  "event": "tool_chunk",
  "event_type": "progress",
  "timestamp": "2023-12-31T12:00:00.000Z",
  "chunk_id": "chunk_1640995200.123",
  "progress": 50,
  "message": "Đang xử lý...",
  "tool_name": "my_tool"
}
```

### Event Types
- `started`: Tool bắt đầu execute
  ```json
  {
    "event": "tool_chunk",
    "event_type": "started",
    "tool_name": "analyze_user_data",
    "arguments": {"analysis_type": "comprehensive"},
    "progress": 0,
    "status": "started"
  }
  ```

- `progress`: Progress update
  ```json
  {
    "event": "tool_chunk",
    "event_type": "progress",
    "progress": 75,
    "message": "Đang phân tích dữ liệu...",
    "status": "progress"
  }
  ```

- `data`: Intermediate data chunk
  ```json
  {
    "event": "tool_chunk",
    "event_type": "data",
    "data_type": "media_stats",
    "message": "Thống kê media files",
    "payload": {"media_count": 150},
    "status": "data"
  }
  ```

- `completed`: Tool hoàn thành thành công
  ```json
  {
    "event": "tool_chunk",
    "event_type": "completed",
    "progress": 100,
    "success": true,
    "result": {"analysis": "completed"},
    "status": "completed"
  }
  ```

- `error`: Tool gặp lỗi
  ```json
  {
    "event": "tool_chunk",
    "event_type": "error",
    "progress": 100,
    "success": false,
    "error": "Authentication failed",
    "status": "error"
  }
  ```

### Legacy Support
- Vẫn hỗ trợ `status` field cho backward compatibility
- `timeout`: Stream polling timeout (client-side)

## 🧪 Testing

### Chạy Test Suite
```bash
python test_streaming_tools.py
```

### Manual Testing
```bash
# 1. Start Redis
docker run -d -p 6379:6379 redis:7-alpine

# 2. Start MCP Server
python src/server/redai_system/shipment/shipment_server.py

# 3. Test với curl
curl -X POST http://localhost:8009/mcp \
  -H "Content-Type: application/json" \
  -H "Stream-Key: test_stream_123" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "create_order",
      "arguments": {
        "to_name": "Test User",
        "to_phone": "0987654321",
        "to_address": "123 Test St",
        "to_ward_code": "20308",
        "to_district_id": 1542,
        "cod_amount": 100000,
        "content": "Test package",
        "weight": 500,
        "length": 20,
        "width": 15,
        "height": 10,
        "service_type_id": 2
      }
    }
  }'

# 4. Poll Redis stream
redis-cli XREAD STREAMS tool_stream:test_stream_123 0
```

## 🔍 Troubleshooting

### Redis Connection Issues
```bash
# Check Redis is running
docker ps | grep redis

# Test Redis connection
redis-cli ping

# Check Redis logs
docker logs <redis_container_id>
```

### Stream Not Working
1. **Kiểm tra X-Stream-Id header**: Client phải gửi header này
2. **Kiểm tra Redis connection**: Server phải kết nối được Redis
3. **Kiểm tra tool decorator**: Tool phải có `@streaming_tool()` decorator
4. **Kiểm tra imports**: Đảm bảo import streaming modules thành công

### Performance Issues
1. **Redis memory**: Monitor Redis memory usage
2. **Stream cleanup**: Expired streams được cleanup tự động
3. **Connection pooling**: Redis client sử dụng connection pool
4. **Concurrent streams**: Redis handle multiple streams efficiently

## 📈 Monitoring

### Redis Streams
```bash
# List all streams
redis-cli --scan --pattern "tool_stream:*"

# Get stream info
redis-cli XINFO STREAM tool_stream:my_stream_id

# Get stream length
redis-cli XLEN tool_stream:my_stream_id
```

### Cleanup
```bash
# Manual cleanup expired streams
redis-cli --scan --pattern "tool_stream:*" | xargs redis-cli DEL
```

## 🚀 Roadmap

- [ ] **WebSocket streaming**: Real-time push thay vì polling
- [ ] **Stream compression**: Giảm Redis memory usage
- [ ] **Stream persistence**: Lưu streams vào database
- [ ] **Stream analytics**: Metrics và monitoring
- [ ] **Multi-tenant streams**: Isolation theo user/tenant
