#!/usr/bin/env python3
"""
Test Script cho Smart Streaming API Tools

Script này test cách smart streaming factory tự động thêm streaming support
cho các API tools được tạo từ OpenAPI spec với events khác nhau cho từng loại API.

Usage:
    python test_smart_streaming_api.py
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
import httpx

# Import stream client
try:
    from src.server.shared.stream_client import RedisStreamClient
except ImportError:
    print("❌ Không thể import stream client. Đ<PERSON>m bảo đã cài đặt dependencies:")
    print("   pip install aioredis>=2.0.0")
    exit(1)


class SmartStreamingAPITester:
    """
    Class để test smart streaming API tools với different event types
    """
    
    def __init__(self, server_url: str = "http://localhost:8007/mcp"):
        self.server_url = server_url
        self.stream_client = RedisStreamClient()
        
    async def test_api_endpoint(
        self,
        tool_name: str,
        expected_endpoint_type: str,
        bearer_token: Optional[str] = None,
        stream_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Test một API endpoint với smart streaming
        
        Args:
            tool_name: Tên tool (operation_id từ OpenAPI)
            expected_endpoint_type: Loại endpoint expected
            bearer_token: Bearer token cho authentication
            stream_key: Stream key (tự generate nếu None)
            
        Returns:
            Dict chứa kết quả test
        """
        
        # Generate stream key nếu không có
        if not stream_key:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            stream_key = f"api_{tool_name}_{timestamp}_{unique_id}"
        
        print(f"🧪 Testing API Tool: {tool_name}")
        print(f"📡 Stream Key: {stream_key}")
        print(f"🎯 Expected Type: {expected_endpoint_type}")
        print("-" * 70)
        
        # Tạo MCP request
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": {}  # Most GET APIs don't need arguments
            }
        }
        
        # Headers với stream key và Bearer token
        headers = {
            "Content-Type": "application/json",
            "Stream-Key": stream_key
        }
        
        if bearer_token:
            headers["Authorization"] = f"Bearer {bearer_token}"
            print(f"🔑 Using Bearer token: {bearer_token[:30]}...")
        
        # Start polling task
        polling_task = asyncio.create_task(
            self._poll_api_stream_events(stream_key, expected_endpoint_type)
        )
        
        # Gọi tool
        tool_result = None
        tool_error = None
        
        try:
            print("🚀 Gọi API tool...")
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.server_url,
                    json=mcp_request,
                    headers=headers
                )
                
                if response.status_code == 200:
                    result_data = response.json()
                    if "result" in result_data:
                        tool_result = result_data["result"]
                        print("✅ API tool completed successfully!")
                    else:
                        tool_error = result_data.get("error", "Unknown error")
                        print(f"❌ API tool failed: {tool_error}")
                else:
                    tool_error = f"HTTP {response.status_code}: {response.text}"
                    print(f"❌ HTTP Error: {tool_error}")
                    
        except Exception as e:
            tool_error = str(e)
            print(f"❌ Exception: {tool_error}")
        
        # Wait for polling to complete
        try:
            await asyncio.wait_for(polling_task, timeout=15.0)
        except asyncio.TimeoutError:
            polling_task.cancel()
            print("⏰ Polling timeout")
        
        print("-" * 70)
        
        return {
            "stream_key": stream_key,
            "tool_name": tool_name,
            "expected_type": expected_endpoint_type,
            "tool_result": tool_result,
            "tool_error": tool_error,
            "success": tool_result is not None
        }
    
    async def _poll_api_stream_events(self, stream_key: str, expected_type: str):
        """Poll API stream events và analyze event patterns"""
        print("📊 Bắt đầu polling API stream events...")
        
        events_received = []
        event_types_seen = set()
        
        try:
            async for update in self.stream_client.poll_stream(stream_key, timeout=45.0):
                event = update.get("event", "unknown")
                event_type = update.get("event_type", "unknown")
                timestamp = update.get("timestamp", "")
                
                events_received.append({
                    "event": event,
                    "event_type": event_type,
                    "timestamp": timestamp,
                    "data": update
                })
                event_types_seen.add(event_type)
                
                # Format timestamp
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = dt.strftime("%H:%M:%S.%f")[:-3]
                    except:
                        time_str = timestamp[:12]
                else:
                    time_str = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                
                # Display events với analysis
                if event == "tool_chunk":
                    if event_type == "started":
                        endpoint_type = update.get("endpoint_type", "unknown")
                        path = update.get("path", "unknown")
                        method = update.get("method", "unknown")
                        
                        print(f"[{time_str}] 🏁 API Started")
                        print(f"                    📍 Endpoint: {method} {path}")
                        print(f"                    🎯 Type: {endpoint_type}")
                        print(f"                    ✅ Expected: {expected_type}")
                        
                        if endpoint_type == expected_type:
                            print(f"                    ✅ Type match!")
                        else:
                            print(f"                    ❌ Type mismatch!")
                        
                    elif event_type == "progress":
                        progress = update.get("progress", 0)
                        message = update.get("message", "")
                        progress_bar = "█" * (progress // 5) + "░" * (20 - progress // 5)
                        print(f"[{time_str}] 📈 [{progress_bar}] {progress}% - {message}")
                        
                    elif event_type == "data_chunk":
                        data_type = update.get("data_type", "unknown")
                        message = update.get("message", "")
                        payload = update.get("payload")
                        
                        print(f"[{time_str}] 📊 Data Chunk: {data_type}")
                        print(f"                    💬 {message}")
                        
                        if payload:
                            try:
                                if isinstance(payload, str):
                                    payload_data = json.loads(payload)
                                else:
                                    payload_data = payload
                                print(f"                    📄 Data: {json.dumps(payload_data, ensure_ascii=False)}")
                            except:
                                print(f"                    📄 Data: {str(payload)[:100]}...")
                    
                    elif event_type in ["fetching_data", "processing_page", "calculating_stats", 
                                       "searching", "filtering", "sorting", "uploading", 
                                       "validating", "transforming"]:
                        # Specific API events
                        progress = update.get("progress", 0)
                        message = update.get("message", "")
                        print(f"[{time_str}] 🔄 {event_type.upper()}: {progress}%")
                        print(f"                    💬 {message}")
                        
                    elif event_type == "completed":
                        print(f"[{time_str}] ✅ API completed successfully!")
                        
                        result = update.get("result")
                        if result:
                            try:
                                if isinstance(result, str) and len(result) > 200:
                                    result_preview = result[:200] + "..."
                                else:
                                    result_preview = str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
                                print(f"                    📄 Result preview: {result_preview}")
                            except:
                                print(f"                    📄 Result: <complex data>")
                        break
                        
                    elif event_type == "error":
                        error = update.get("error", "Unknown error")
                        print(f"[{time_str}] ❌ API failed: {error}")
                        break
                        
                    else:
                        print(f"[{time_str}] ❓ Unknown event_type: {event_type}")
                        
        except Exception as e:
            print(f"❌ Lỗi polling API stream events: {e}")
        
        # Analysis summary
        print(f"\n📊 Event Analysis:")
        print(f"   Total events: {len(events_received)}")
        print(f"   Event types: {sorted(event_types_seen)}")
        print(f"   Expected type: {expected_type}")
        
        # Analyze event pattern
        if "started" in event_types_seen and "completed" in event_types_seen:
            print(f"   ✅ Complete event flow")
        else:
            print(f"   ❌ Incomplete event flow")
    
    async def run_tests(self):
        """Chạy tất cả tests cho Smart Streaming API"""
        print("=" * 80)
        print("🧪 SMART STREAMING API TOOLS TEST")
        print("=" * 80)
        
        # Connect to Redis
        await self.stream_client.connect()
        
        if not self.stream_client.redis:
            print("❌ Không thể kết nối Redis. Đảm bảo Redis đang chạy:")
            print("   docker-compose up redis -d")
            return
        
        print("✅ Kết nối Redis thành công!")
        print()
        
        # Test Bearer token (optional)
        test_bearer_token = None  # Thay bằng token thật nếu có
        
        # Test cases cho different API types
        test_cases = [
            {
                "name": "List Media Files (Paginated)",
                "tool": "get_user_data_media",  # GET /user/data/media
                "expected_type": "list_paginated"
            },
            {
                "name": "List URL Resources (Paginated)", 
                "tool": "get_user_data_urls",   # GET /user/data/urls
                "expected_type": "list_paginated"
            },
            {
                "name": "Get Statistics",
                "tool": "get_user_data_statistics",  # GET /user/data/statistics
                "expected_type": "statistics"
            },
            {
                "name": "Search Media",
                "tool": "search_user_media",  # GET /user/data/media/search
                "expected_type": "search_complex"
            },
            {
                "name": "Get Single Media",
                "tool": "get_user_media_by_id",  # GET /user/data/media/{id}
                "expected_type": "single_item"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}/{len(test_cases)}: {test_case['name']}")
            
            try:
                result = await self.test_api_endpoint(
                    tool_name=test_case["tool"],
                    expected_endpoint_type=test_case["expected_type"],
                    bearer_token=test_bearer_token
                )
                results.append(result)
                
                if result["success"]:
                    print("✅ Test PASSED")
                else:
                    print("❌ Test FAILED")
                    
            except Exception as e:
                print(f"❌ Test ERROR: {e}")
                results.append({
                    "tool_name": test_case["tool"],
                    "success": False,
                    "error": str(e)
                })
            
            print()
        
        # Summary
        print("=" * 80)
        print("📊 SMART STREAMING API TEST SUMMARY")
        print("=" * 80)
        
        passed = sum(1 for r in results if r.get("success", False))
        total = len(results)
        
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {passed/total*100:.1f}%" if total > 0 else "N/A")
        
        print("\n📋 API Endpoint Test Results:")
        for result in results:
            tool = result.get("tool_name", "unknown")
            expected = result.get("expected_type", "unknown")
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            print(f"   {tool} ({expected}): {status}")
        
        # Cleanup
        await self.stream_client.disconnect()


async def main():
    """Main function"""
    tester = SmartStreamingAPITester()
    await tester.run_tests()


if __name__ == "__main__":
    print("🚀 Starting Smart Streaming API Test...")
    print("📋 Đảm bảo các services đang chạy:")
    print("   - Redis: docker-compose up redis -d")
    print("   - Data Module Server: python src/server/redai_system/data/data_module_server.py")
    print()
    print("🎯 Test sẽ verify:")
    print("   - Smart endpoint type detection")
    print("   - Different streaming events cho từng API type")
    print("   - tool_chunk events với appropriate progress")
    print("   - API-specific event types (fetching_data, calculating_stats, etc.)")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
