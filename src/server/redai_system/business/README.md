# RedAI Business User Module MCP Server

Server MCP cho RedAI Business User Module API sử dụng FastMCP và OpenAPI Schema.

## Tính năng

- **Order Management**: Quản lý đơn hàng và tracking vận chuyển
- **Business Core**: Qu<PERSON>n lý trường tùy chỉnh và báo cáo business
- **Customer Management**: <PERSON><PERSON><PERSON><PERSON> lý khách hàng và chuyển đổi
- **Address Management**: Quản lý địa chỉ người dùng và shop
- **Warehouse & Inventory**: Quản lý kho hàng và tồn kho
- **Product Management**: Quản lý sản phẩm với variant và media
- **Shipping Integration**: Tích hợp GHN và GHTK vận chuyển

## Chạy với Docker

### 1. Build và chạy với Docker

```bash
# Build image
docker build -t redai-business-server .

# Chạy container
docker run -d \
  --name redai-business-server \
  -p 8010:8010 \
  -e REDAI_BUSINESS_API_BASE_URL=https://api.redai.com \
  redai-business-server
```

### 2. Sử dụng Docker Compose (Khuyến nghị)

```bash
# Chạy tất cả services (từ root directory)
docker-compose up -d

# Chỉ chạy business service
docker-compose up -d redai-business

# Xem logs
docker-compose logs -f redai-business

# Dừng services
docker-compose down
```

## Cấu hình Environment Variables

| Variable | Mặc định | Mô tả |
|----------|----------|-------|
| `REDAI_BUSINESS_API_BASE_URL` | `https://api.redai.com` | Base URL của Business API |
| `BUSINESS_HTTP_HOST` | `0.0.0.0` | Host để bind server |
| `BUSINESS_HTTP_PORT` | `8010` | Port của MCP server |
| `BUSINESS_HTTP_PATH` | `/mcp` | Path endpoint cho MCP |
| `BUSINESS_TRANSPORT` | `streamable-http` | Transport method |

## Endpoints

- **MCP Server**: `http://localhost:8010/mcp`
- **Health Check**: `http://localhost:8010/health`

## Chạy Development

```bash
# Cài đặt dependencies
pip install -r requirements.txt

# Chạy server
python business_server.py

# Hoặc với transport cụ thể
python business_server.py streamable-http
```

## API Tools

Server tự động tạo tools từ `swagger.json` và cung cấp các tools đặc biệt:

- `get_business_summary`: Lấy tổng quan Business User Module
- `validate_shipping_config`: Kiểm tra cấu hình vận chuyển (GHN/GHTK)
- `get_business_endpoints_by_category`: Lọc endpoints theo danh mục

## Authentication

Server sử dụng Bearer Token authentication:
- Token được truyền từ client trong Authorization header
- Tự động sử dụng token cho tất cả API calls

## Logs

Logs được lưu trong thư mục `/app/logs` trong container và có thể mount ra host:

```bash
docker run -v ./logs:/app/logs redai-business-server
```
