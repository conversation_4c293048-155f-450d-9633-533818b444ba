{"openapi": "3.0.0", "info": {"title": "Business User Module API", "description": "API documentation for Business User Module - <PERSON><PERSON><PERSON><PERSON> lý hệ thống business cho người dùng bao gồm đơn hàng, s<PERSON><PERSON> phẩm, kho hàng, vận chuyển và báo cáo", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Order", "description": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng cho người dùng"}, {"name": "User - Order Tracking", "description": "Tracking và webhook cho đơn hàng vận chuyển"}, {"name": "User - Business", "description": "<PERSON><PERSON><PERSON><PERSON> lý trường tùy chỉnh cho business"}, {"name": "User - Convert", "description": "<PERSON><PERSON><PERSON><PERSON> lý bản ghi chuyển đổi khách hàng"}, {"name": "User - Convert Customer", "description": "<PERSON><PERSON><PERSON><PERSON> lý khách hàng chuyển đổi"}, {"name": "User - Business Report", "description": "Báo cáo business cho người dùng"}, {"name": "User - Address", "description": "<PERSON><PERSON><PERSON><PERSON> lý địa chỉ người dùng"}, {"name": "User - Shop Address", "description": "<PERSON><PERSON><PERSON>n lý địa chỉ shop của người dùng"}, {"name": "User - Warehouse", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho hàng của người dùng"}, {"name": "User - Physical Warehouse", "description": "<PERSON><PERSON><PERSON><PERSON> lý kho vật lý của người dùng"}, {"name": "User - Inventory", "description": "<PERSON><PERSON><PERSON><PERSON> lý tồn kho của người dùng"}, {"name": "User - Business Customer Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm khách hàng"}, {"name": "User - Business Physical Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm vật lý"}, {"name": "User - Business Digital Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm số"}, {"name": "User - Business Event Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm sự kiện"}, {"name": "User - Business Service Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm dịch vụ"}, {"name": "User - Business Combo Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm combo"}, {"name": "User - Business Entity Has Media", "description": "Quản lý media cho các entity business"}, {"name": "User - Business Simple Customer Product", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm khách hàng đơn giản"}, {"name": "User - Location", "description": "<PERSON><PERSON><PERSON><PERSON> lý địa chỉ (tỉnh/thành phố, phường/xã)"}, {"name": "User - Address", "description": "<PERSON><PERSON><PERSON><PERSON> lý địa chỉ người dùng"}, {"name": "User - Shop Address", "description": "<PERSON><PERSON><PERSON>n lý địa chỉ shop của người dùng"}, {"name": "User - Convert", "description": "<PERSON><PERSON><PERSON><PERSON> lý bản ghi chuyển đổi khách hàng"}, {"name": "User - Convert Customer", "description": "<PERSON><PERSON><PERSON><PERSON> lý khách hàng chuyển đổi"}, {"name": "User - Business Report", "description": "Báo cáo business cho người dùng"}, {"name": "User - Order Tracking", "description": "Tracking và webhook cho đơn hàng vận chuyển"}, {"name": "Payment - Integration", "description": "<PERSON><PERSON><PERSON><PERSON> lý mã QR thanh toán"}, {"name": "GHN - Address", "description": "<PERSON><PERSON><PERSON>n lý địa chỉ GHN"}, {"name": "GHN - Shipment", "description": "<PERSON><PERSON><PERSON><PERSON> lý vận chuyển GHN"}, {"name": "GHTK - Shipment", "description": "<PERSON><PERSON><PERSON><PERSON> lý vận chuyển GHTK"}], "paths": {"/user/orders/draft": {"post": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> đơn hàng", "description": "T<PERSON><PERSON> đơn hàng và tự động confirmed, gử<PERSON> đến nhà vận chuyển ngay lập tức. <PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON><PERSON><PERSON> confirm riê<PERSON> biệt.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDraftOrderDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> đơn hàng thành công và đã đ<PERSON><PERSON><PERSON> confirmed", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đơn hàng thành công và đã đ<PERSON><PERSON><PERSON> confirmed"}, "result": {"$ref": "#/components/schemas/UserOrderResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders": {"get": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch đơn hàng", "description": "<PERSON><PERSON><PERSON> danh sách đơn hàng với phân trang và tìm kiếm", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON><PERSON> kiếm theo mã đơn hàng hoặc tên khách hàng", "required": false, "schema": {"type": "string", "example": "ORD-001"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái đơn hàng", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "CONFIRMED", "SHIPPED", "DELIVERED", "CANCELLED", "RETURNED"], "example": "CONFIRMED"}}, {"name": "shippingStatus", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái vận chuyển", "required": false, "schema": {"type": "string", "enum": ["PENDING", "PICKED_UP", "IN_TRANSIT", "DELIVERED", "FAILED", "RETURNED"], "example": "IN_TRANSIT"}}, {"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> l<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kết thúc lọ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách đơn hàng thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserOrderListItemDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/detail/{id}": {"get": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> chi tiết đơn hàng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một đơn hàng theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết đơn hàng thành công"}, "result": {"$ref": "#/components/schemas/UserOrderResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}": {"patch": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng", "description": "<PERSON><PERSON><PERSON> nhật thông tin đơn hàng theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserOrderDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng thành công"}, "result": {"$ref": "#/components/schemas/UserOrderResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/status-stats": {"get": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> thống kê trạng thái đơn hàng", "description": "<PERSON><PERSON><PERSON> thống kê trạng thái đơn hàng và vận chuyển của người dùng", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê trạng thái đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê trạng thái đơn hàng thành công"}, "result": {"$ref": "#/components/schemas/UserOrderStatusResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/track": {"get": {"tags": ["User - Order"], "summary": "Tracking <PERSON><PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON> thông tin tracking c<PERSON>a đơn hàng", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tracking thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tracking thành công"}, "result": {"$ref": "#/components/schemas/TrackOrderResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/cancel": {"post": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> đơn hàng", "description": "<PERSON><PERSON><PERSON> đơn hàng theo <PERSON>", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelOrderRequestDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đơn hàng thành công"}, "result": {"$ref": "#/components/schemas/CancelOrderResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/return": {"post": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> trả đơn hàng", "description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu hoàn trả đơn hàng", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReturnOrderRequestDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu hoàn trả thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> y<PERSON>u cầu hoàn trả thành công"}, "result": {"$ref": "#/components/schemas/ReturnOrderResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/delivery-again": {"post": {"tags": ["User - Order"], "summary": "<PERSON><PERSON>o lại đơn hàng", "description": "<PERSON><PERSON><PERSON> yêu cầu giao lại đơn hàng", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryAgainOrderRequestDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu giao lại thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> y<PERSON>u cầu giao lại thành công"}, "result": {"$ref": "#/components/schemas/DeliveryAgainOrderResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/bulk": {"delete": {"tags": ["User - Order"], "summary": "<PERSON><PERSON><PERSON> nhi<PERSON>u đơn hàng", "description": "<PERSON><PERSON><PERSON> nhiều đơn hàng cùng lúc", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteUserOrderDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhiều đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhiều đơn hàng thành công"}, "result": {"$ref": "#/components/schemas/BulkDeleteUserOrderResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/address/provinces": {"get": {"tags": ["User - Location"], "summary": "<PERSON><PERSON><PERSON> danh sách tỉnh/thành phố", "description": "API này lấy danh sách tất cả tỉnh/thành phố từ hệ thống địa chỉ mới (2 cấp)", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách tỉnh/thành phố thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách tỉnh/thành phố thành công"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/ProvinceResponseDto"}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/address/wards": {"get": {"tags": ["User - Location"], "summary": "<PERSON><PERSON><PERSON> danh sách phường/xã theo tỉnh", "description": "API này lấy danh sách phường/xã theo tỉnh/thành phố từ hệ thống địa chỉ mới (2 cấp)", "parameters": [{"name": "provinceId", "in": "query", "description": "ID tỉnh/thành phố", "required": true, "schema": {"type": "integer", "example": 202}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách phường/xã thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách phường/xã thành công"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/WardResponseDto"}}}}}}}, "400": {"description": "<PERSON><PERSON><PERSON><PERSON> tham số provinceId", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 400}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tham số provinceId"}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/addresses": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> tất cả địa chỉ của user", "description": "<PERSON><PERSON><PERSON> danh sách tất cả địa chỉ của user hiệ<PERSON> tại (không phân trang)", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ mới", "description": "Tạo địa chỉ mới cho user. Sử dụng UUID từ location service cho provinceUuid và wardUuid. Nếu isDefault = true, địa chỉ này sẽ trở thành địa chỉ mặc định", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserAddressV2Dto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> địa chỉ thành công"}, "result": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/addresses/paginated": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ có phân trang", "description": "<PERSON><PERSON><PERSON> danh sách địa chỉ của user với phân trang và tìm kiếm", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON><PERSON> kiếm theo tên hoặc địa chỉ", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/addresses/{id}": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> chi tiết địa chỉ", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một địa chỉ cụ thể", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết địa chỉ thành công"}, "result": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t địa chỉ", "description": "<PERSON><PERSON><PERSON> nhật thông tin địa chỉ theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserAddressV2Dto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật địa chỉ thành công"}, "result": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ", "description": "<PERSON><PERSON>a địa chỉ theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> địa chỉ thành công"}, "result": {"type": "object", "nullable": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/addresses/{id}/set-default": {"put": {"tags": ["User - Address"], "summary": "Đặt địa chỉ làm mặc định", "description": "Đặt một địa chỉ cụ thể làm địa chỉ mặc định. Các địa chỉ mặc định khác sẽ tự động bị bỏ mặc định.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Đặt địa chỉ mặc định thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Đặt địa chỉ mặc định thành công"}, "result": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/addresses/default/current": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ mặc định hiện tại", "description": "<PERSON><PERSON><PERSON> thông tin địa chỉ mặc định hiện tại của user", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> địa chỉ mặc định thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> địa chỉ mặc định thành công"}, "result": {"$ref": "#/components/schemas/UserAddressV2ResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy địa chỉ mặc định", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 404}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tìm thấy địa chỉ mặc định"}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/shop-address-v2": {"get": {"tags": ["User - Shop Address"], "summary": "<PERSON><PERSON><PERSON> danh sách địa chỉ shop v2 với phân trang", "description": "API này cho phép user lấy danh sách địa chỉ shop v2 của mình với thông tin location đầy đủ và hỗ trợ phân trang, tì<PERSON> kiếm, sắp xếp", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (mặc định: 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng mỗi trang (mặc định: 10)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON>m kiếm theo tên shop hoặc địa chỉ", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "required": false, "schema": {"type": "string", "enum": ["shopName", "createdAt", "updatedAt"], "example": "createdAt"}}, {"name": "sortOrder", "in": "query", "description": "<PERSON><PERSON><PERSON> tự sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "example": "DESC"}}, {"name": "isDefault", "in": "query", "description": "<PERSON><PERSON><PERSON> theo địa chỉ mặc định", "required": false, "schema": {"type": "boolean", "example": true}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách địa chỉ shop v2 thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách địa chỉ shop v2 thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserShopAddressV2ResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Shop Address"], "summary": "Tạo địa chỉ shop v2 mới", "description": "Tạo một địa chỉ shop mới với hệ thống 2 cấp địa chỉ. Sử dụng UUID từ location service", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserShopAddressV2Dto"}, "examples": {"create_shop_address": {"summary": "Tạo địa chỉ shop vớ<PERSON> hệ thống 2 cấp", "description": "Sử dụng provinceId và wardId (UUID) từ API /provinces và /wards", "value": {"shopName": "<PERSON><PERSON><PERSON>", "shopPhone": "0123456789", "shopAddress": "123 <PERSON><PERSON><PERSON><PERSON>", "provinceId": "d2b3a28d-209d-4a89-b21d-140fb8d84bac", "wardId": "dbc1ab9f-d199-475b-8b10-7f83ed5019df", "isDefault": false}}}}}}, "responses": {"201": {"description": "Tạo địa chỉ shop thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "Tạo địa chỉ shop thành công"}, "result": {"$ref": "#/components/schemas/UserShopAddressV2ResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Shop Address"], "summary": "Xóa nhiều shop v2", "description": "API này cho phép user xóa nhiều shop v2 cùng lúc", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteMultipleShopsV2Dto"}}}}, "responses": {"200": {"description": "Xóa nhiều shop v2 thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Xóa nhiều shop v2 thành công"}, "data": {"type": "object", "properties": {"deletedCount": {"type": "integer", "example": 2}, "totalRequested": {"type": "integer", "example": 3}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/shop-address-v2/{id}": {"get": {"tags": ["User - Shop Address"], "summary": "Lấy thông tin shop v2 theo ID", "description": "API này cho phép user lấy thông tin chi tiết của một shop v2 cụ thể", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của shop v2", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Lấy thông tin shop v2 thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Lấy thông tin shop v2 thành công"}, "data": {"$ref": "#/components/schemas/UserShopAddressV2ResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Shop Address"], "summary": "Cập nhật shop v2 theo ID", "description": "API này cho phép user cập nhật shop v2 theo ID với validation location", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của shop v2", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserShopAddressV2Dto"}}}}, "responses": {"200": {"description": "Cập nhật shop v2 thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Cập nhật shop v2 thành công"}, "data": {"$ref": "#/components/schemas/UserShopAddressV2ResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/shop-address-v2/{id}/set-default": {"put": {"tags": ["User - Shop Address"], "summary": "Đặt địa chỉ shop v2 làm mặc định", "description": "API này cho phép user đặt một địa chỉ shop v2 cụ thể làm mặc định. Các địa chỉ khác sẽ tự động bỏ mặc định.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của shop v2", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Đặt địa chỉ shop v2 mặc định thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đặt địa chỉ shop v2 mặc định thành công"}, "data": {"$ref": "#/components/schemas/UserShopAddressV2ResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/customer-products/bulk": {"post": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> <PERSON> sản phẩm khách hàng cùng lúc", "description": "<PERSON><PERSON><PERSON> nhiều sản phẩm khách hàng cùng lúc. Tối đa 200 sản phẩm mỗi lần. X<PERSON> lý qua queue để tối ưu performance.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCreateCustomerProductDto"}}}}, "responses": {"202": {"description": "<PERSON><PERSON><PERSON> cầu tạo nhiều sản phẩm đã đư<PERSON><PERSON> tiếp nhận", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 202}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> cầu tạo nhiều sản phẩm đã đư<PERSON><PERSON> tiếp nhận"}, "result": {"$ref": "#/components/schemas/BulkCreateCustomerProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> sản phẩm khách hàng", "description": "<PERSON><PERSON><PERSON> nhi<PERSON>u sản phẩm khách hàng cùng lúc theo danh sách ID", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeleteCustomerProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhi<PERSON>u sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhi<PERSON>u sản phẩm thành công"}, "result": {"$ref": "#/components/schemas/BulkDeleteCustomerProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/customer-products/import": {"post": {"tags": ["User - Business Customer Product"], "summary": "Import sản phẩm từ file", "description": "Import sản phẩm khách hàng từ file Excel/CSV/Word. Hỗ trợ các định dạng: .xlsx, .xls, .csv, .docx, .doc", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "File Excel/CSV/Word chứa danh sách sản phẩm"}, "productType": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "description": "<PERSON><PERSON><PERSON> sản phẩm (tất cả sản phẩm trong file phải cùng loại)", "example": "PHYSICAL"}}, "required": ["file"]}}}}, "responses": {"202": {"description": "File đã đ<PERSON><PERSON><PERSON> upload và đang xử lý", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 202}, "message": {"type": "string", "example": "File đã đ<PERSON><PERSON><PERSON> upload và đang xử lý"}, "result": {"$ref": "#/components/schemas/ImportCustomerProductsResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/customer-products/import/{jobId}/status": {"get": {"tags": ["User - Business Customer Product"], "summary": "Kiểm tra trạng thái import", "description": "<PERSON><PERSON><PERSON> tra trạng thái xử lý import s<PERSON><PERSON> phẩm theo job ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "jobId", "in": "path", "description": "ID của job import", "required": true, "schema": {"type": "string", "example": "job_123456"}}], "responses": {"200": {"description": "Lấy trạng thái import thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Lấy trạng thái import thành công"}, "result": {"type": "object", "properties": {"jobId": {"type": "string", "example": "job_123456"}, "status": {"type": "string", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"], "example": "PROCESSING"}, "progress": {"type": "number", "example": 75}, "totalItems": {"type": "number", "example": 100}, "processedItems": {"type": "number", "example": 75}, "successItems": {"type": "number", "example": 70}, "failedItems": {"type": "number", "example": 5}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/physical/{id}/complete": {"get": {"tags": ["User - Business Physical Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết hoàn chỉnh sản phẩm vật lý", "description": "<PERSON><PERSON>y thông tin chi tiết hoàn chỉnh sản phẩm vật lý bao gồm: thông tin cơ bản từ customer_products, thông tin vật lý từ physical_products (SKU, barcode, shipmentConfig), danh sách variants từ physical_product_variants, danh sách images từ entity_has_media, thông tin tồn kho từ product_inventory", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> của sản ph<PERSON>m vật lý", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm vật lý thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm vật lý thành công"}, "result": {"$ref": "#/components/schemas/CompletePhysicalProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Physical Product"], "summary": "<PERSON><PERSON><PERSON> nhật hoàn chỉnh sản phẩm vật lý", "description": "<PERSON><PERSON><PERSON> nhật toàn bộ thông tin sản phẩm vật lý bao gồm thông tin cơ bản, variants và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> của sản ph<PERSON>m vật lý", "required": true, "schema": {"type": "integer", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdatePhysicalProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật sản phẩm vật lý thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật sản phẩm vật lý thành công"}, "result": {"$ref": "#/components/schemas/CompletePhysicalProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/digital/{id}/complete": {"get": {"tags": ["User - Business Digital Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết hoàn chỉnh sản phẩm số", "description": "<PERSON><PERSON>y toàn bộ thông tin chi tiết của sản phẩm số bao gồm: thông tin cơ bản từ customer_products, thông tin số hóa từ digital_products, lo<PERSON>i sản phẩm số (digitalProductType), li<PERSON><PERSON> k<PERSON> t<PERSON> cậ<PERSON> (accessLink), hư<PERSON>ng dẫn sử dụng (usageInstruction), danh sách phiên bản từ digital_product_versions, hình ảnh product level và version level, custom fields, thông tin tồn kho từ product_inventory", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m số", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm số thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm số thành công"}, "result": {"$ref": "#/components/schemas/CompleteDigitalProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Digital Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t hoàn chỉnh sản phẩm số", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm số bao gồm thông tin cơ bản, versions và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m số", "required": true, "schema": {"type": "integer", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateDigitalProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm số thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm số thành công"}, "result": {"$ref": "#/components/schemas/CompleteDigitalProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/event/{id}/complete": {"get": {"tags": ["User - Business Event Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết hoàn chỉnh sản phẩm sự kiện", "description": "<PERSON><PERSON>y thông tin chi tiết hoàn chỉnh sản phẩm sự kiện bao gồm: thông tin cơ bản từ customer_products, thông tin sự kiện từ event_products, danh sách vé từ event_product_tickets, danh sách images từ entity_has_media, thông tin tồn kho từ product_inventory (nếu có)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID c<PERSON>a sản phẩm sự kiện", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm sự kiện thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm sự kiện thành công"}, "result": {"$ref": "#/components/schemas/CompleteEventProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Event Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hoàn chỉnh sản phẩm sự kiện", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm sự kiện bao gồm thông tin cơ bản, tickets và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID c<PERSON>a sản phẩm sự kiện", "required": true, "schema": {"type": "integer", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateEventProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm sự kiện thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm sự kiện thành công"}, "result": {"$ref": "#/components/schemas/CompleteEventProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/service/{id}/complete": {"get": {"tags": ["User - Business Service Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết hoàn chỉnh sản phẩm dịch vụ", "description": "<PERSON><PERSON>y thông tin chi tiết hoàn chỉnh sản phẩm dịch vụ bao gồm: thông tin cơ bản từ customer_products, thông tin dịch vụ từ service_products, danh sách gói dịch vụ từ service_packages_option, danh sách images từ entity_has_media, thông tin tồn kho từ product_inventory với service_packages_option_id", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của sản ph<PERSON>m dịch vụ", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm dịch vụ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm dịch vụ thành công"}, "result": {"$ref": "#/components/schemas/CompleteServiceProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Service Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t hoàn chỉnh sản phẩm dịch vụ", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm dịch vụ bao gồm thông tin cơ bản, service packages và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của sản ph<PERSON>m dịch vụ", "required": true, "schema": {"type": "integer", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateServiceProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> sản phẩm dịch vụ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> sản phẩm dịch vụ thành công"}, "result": {"$ref": "#/components/schemas/CompleteServiceProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/combo/{id}": {"get": {"tags": ["User - Business Combo Product"], "summary": "<PERSON><PERSON>y chi tiết hoàn chỉnh combo product", "description": "<PERSON><PERSON>y toàn bộ thông tin chi tiết của combo product bao gồm: thông tin cơ bản (tên, mô tả, tags, status), thông tin giá (price, typePrice), thông tin combo (maxQuantity, purchaseCount, totalItems, isAvailable), custom fields, hình ảnh combo level, thông tin nâng cao với combo items chi tiết, thông tin sản phẩm con trong combo (tên, loại, giá, ảnh đại diện)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của combo product cần l<PERSON>y chi tiết", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON>y chi tiết combo product thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON>y chi tiết combo product thành công"}, "result": {"$ref": "#/components/schemas/CompleteComboProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Combo Product"], "summary": "<PERSON><PERSON><PERSON> nhật hoàn chỉnh combo product", "description": "<PERSON><PERSON><PERSON> nhật toàn bộ thông tin combo product bao gồm thông tin cơ bản, combo items và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của combo product", "required": true, "schema": {"type": "integer", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateComboProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON>p nhật combo product thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON>p nhật combo product thành công"}, "result": {"$ref": "#/components/schemas/CompleteComboProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/simple-customer-products": {"get": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng đơn giản", "description": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng với phân trang và tìm kiếm (phiên bản đơn giản)", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> kiếm theo tên sản phẩm", "required": false, "schema": {"type": "string", "example": "iPhone"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng đơn giản thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleCustomerProductResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> sản phẩm khách hàng đơn giản", "description": "<PERSON><PERSON><PERSON> sản phẩm khách hàng với 3 thông tin cơ bản: tê<PERSON>, mô tả, lo<PERSON><PERSON> sản phẩm", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateCustomerProductDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> sản phẩm khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> sản phẩm khách hàng thành công"}, "result": {"$ref": "#/components/schemas/SimpleCustomerProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/simple-customer-products/{id}": {"get": {"tags": ["User - Business Simple Customer Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm khách hàng đơn giản", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết sản phẩm khách hàng theo ID với format đơn giản", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của sản ph<PERSON>m kh<PERSON>ch hàng", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm đơn giản thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm khách hàng thành công"}, "result": {"$ref": "#/components/schemas/SimpleCustomerProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/entity-has-media": {"get": {"tags": ["User - Business Entity Has Media"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch liên kết media", "description": "<PERSON><PERSON>y danh sách liên kết media với entity với phân trang và filter theo các entity khác nhau", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "entityType", "in": "query", "description": "Loại entity", "required": false, "schema": {"type": "string", "enum": ["product", "physicalVarial", "ticketVarial", "version", "combo", "plan"], "example": "product"}}, {"name": "entityId", "in": "query", "description": "ID của entity", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách liên kết media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách liên kết media thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EntityHasMediaResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Business Entity Has Media"], "summary": "Tạo liên kết media với entity", "description": "Tạo liên kết mới giữa media và entity (product, physicalVarial, ticketVarial, version, combo, plan). Chỉ có thể liên kết với một entity tại một thời điểm.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEntityHasMediaDto"}}}}, "responses": {"201": {"description": "Tạo liên kết media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "Tạo liên kết media thành công"}, "result": {"$ref": "#/components/schemas/EntityHasMediaResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/entity-has-media/{id}": {"get": {"tags": ["User - Business Entity Has Media"], "summary": "<PERSON><PERSON><PERSON> chi tiết liên kết media", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một liên kết media theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của liên kế<PERSON> media", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết liên kết media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết liên kết media thành công"}, "result": {"$ref": "#/components/schemas/EntityHasMediaResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "patch": {"tags": ["User - Business Entity Has Media"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> liên kết media", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin liên kết media theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của liên kế<PERSON> media", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEntityHasMediaDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật liên kết media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật liên kết media thành công"}, "result": {"$ref": "#/components/schemas/EntityHasMediaResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Business Entity Has Media"], "summary": "Xóa liên k<PERSON> media", "description": "Xóa liên kết media theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của liên kế<PERSON> media", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Xóa liên kết media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Xóa liên kết media thành công"}, "result": {"type": "object", "nullable": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/entity-has-media/entity/{entityType}/{entityId}": {"get": {"tags": ["User - Business Entity Has Media"], "summary": "Lấy danh sách media theo entity", "description": "Lấy danh sách media liên kết với một entity cụ thể. EntityType có thể là: product, physicalVarial, ticketVarial, version, combo, plan.", "security": [{"bearerAuth": []}], "parameters": [{"name": "entityType", "in": "path", "description": "Loại entity (product, physicalVarial, ticketVarial, version, combo, plan)", "required": true, "schema": {"type": "string", "enum": ["product", "physicalVarial", "ticketVarial", "version", "combo", "plan"], "example": "product"}}, {"name": "entityId", "in": "path", "description": "ID của entity", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "Danh sách media của entity", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Lấy danh sách media theo entity thành công"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/EntityHasMediaResponseDto"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/payment-qr/order/{orderId}": {"get": {"tags": ["User - Payment QR"], "summary": "Lấy QR Code cho đơn hàng thường", "description": "Tạo URL QR Code thanh toán cho đơn hàng với mã REDAI{orderId}SEPAYHUB", "security": [{"bearerAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "<PERSON><PERSON> đơn hàng", "required": true, "schema": {"type": "string", "example": "12345"}}, {"name": "amount", "in": "query", "description": "<PERSON><PERSON> tiền cần thanh toán", "required": true, "schema": {"type": "number", "example": 100000}}], "responses": {"200": {"description": "Lấy QR Code thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Lấy QR Code thành công"}, "result": {"type": "object", "properties": {"qrCodeUrl": {"type": "string", "example": "https://qr.sepay.vn/img?bank=970422&acc=**********&template=compact&amount=100000.00&des=REDAI12345SEPAYHUB"}, "description": {"type": "string", "example": "REDAI12345SEPAYHUB"}, "amount": {"type": "number", "example": 100000}, "orderId": {"type": "string", "example": "12345"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/payment-qr/subscription/{orderId}": {"get": {"tags": ["Payment - Integration"], "summary": "Lấy QR Code cho đơn hàng subscription", "description": "Tạo URL QR Code thanh toán cho đơn hàng subscription với mã REDAI{orderId}SEPAYHUBSUB", "security": [{"bearerAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "Mã đơn hàng subscription", "required": true, "schema": {"type": "string", "example": "12345"}}, {"name": "amount", "in": "query", "description": "<PERSON><PERSON> tiền cần thanh toán", "required": true, "schema": {"type": "number", "example": 100000}}], "responses": {"200": {"description": "Lấy QR Code thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Lấy QR Code thành công"}, "result": {"type": "object", "properties": {"qrCodeUrl": {"type": "string", "example": "https://img.vietqr.io/image/970415-0123456789-compact2.jpg?amount=100000&addInfo=REDAI12345SEPAYHUBSUB"}, "description": {"type": "string", "example": "REDAI12345SEPAYHUBSUB"}, "amount": {"type": "number", "example": 100000}, "orderId": {"type": "string", "example": "12345"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/tracking": {"get": {"tags": ["User - Order Tracking"], "summary": "Tracking trạng thái đơn hàng", "description": "<PERSON><PERSON><PERSON> thông tin tracking từ đơn vị vận chuyển và cập nhật trạng thái đơn hàng", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tracking thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tracking thành công"}, "data": {"$ref": "#/components/schemas/TrackingApiResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/update-tracking": {"post": {"tags": ["User - Order Tracking"], "summary": "<PERSON><PERSON><PERSON> tracking đ<PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> thông tin tracking cho đơn hàng từ webhook hoặc manual update", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"trackingCode": {"type": "string", "description": "<PERSON>ã <PERSON> từ nhà vận chuyển", "example": "GHN123456789"}, "status": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái vận chuyển", "enum": ["PENDING", "PICKED_UP", "IN_TRANSIT", "DELIVERED", "FAILED", "RETURNED"], "example": "IN_TRANSIT"}, "note": {"type": "string", "description": "<PERSON><PERSON> chú thêm", "example": "<PERSON><PERSON> vận chuyển đến kho phân loại"}}, "required": ["trackingCode", "status"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> tracking thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tracking thành công"}, "data": {"type": "object", "properties": {"orderId": {"type": "integer", "example": 1}, "trackingCode": {"type": "string", "example": "GHN123456789"}, "status": {"type": "string", "example": "IN_TRANSIT"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business-report/overview": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON><PERSON> dữ liệu tổng quan báo cáo", "description": "API lấy dữ liệu tổng quan bao gồm tổng doanh thu, tổng đơn hàng, kh<PERSON><PERSON> hàng mới và so s<PERSON>h với kỳ trước", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}, {"name": "period", "in": "query", "description": "<PERSON><PERSON> báo cáo", "required": false, "schema": {"type": "string", "enum": ["today", "yesterday", "this_week", "last_week", "this_month", "last_month", "this_year", "custom"], "example": "this_month"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dữ liệu tổng quan thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> dữ liệu tổng quan thành công"}, "result": {"$ref": "#/components/schemas/ReportOverviewResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business-report/sales-chart": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ doanh thu", "description": "API lấy dữ liệu biểu đồ doanh thu theo thời gian. <PERSON><PERSON> thống tự động nhóm dữ liệu dựa trên khoảng thời gian: ≤7 ngày (theo ngày), ≤90 ngày (theo tuầ<PERSON>), ≤730 ngày (theo tháng), >730 ngày (theo quý)", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ doanh thu thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ doanh thu thành công"}, "result": {"$ref": "#/components/schemas/SalesChartResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business-report/top-products": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON>y top sản ph<PERSON>m b<PERSON> ch<PERSON>y", "description": "API lấy danh sách top sản phẩm bán chạy nhất trong khoảng thời gian", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng sản phẩm top (mặc định: 10)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> top sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> top sản phẩm thành công"}, "result": {"$ref": "#/components/schemas/TopProductsResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business-report/customer-analytics": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON><PERSON> phân tích khách hàng", "description": "API lấy dữ liệu phân tích khách hàng bao gồm khách hàng mới, khách hàng quay lại, tỷ lệ chuyển đổi", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> phân tích khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> phân tích khách hàng thành công"}, "result": {"$ref": "#/components/schemas/CustomerAnalyticsResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/convert": {"get": {"tags": ["User - Convert"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch bản ghi chuyển đổi", "description": "<PERSON><PERSON><PERSON> danh sách bản ghi chuyển đổi khách hàng với phân trang và lọc", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON><PERSON> kiếm theo tên hoặc email khách hàng", "required": false, "schema": {"type": "string", "example": "<EMAIL>"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái chuyển đổi", "required": false, "schema": {"type": "string", "enum": ["PENDING", "CONVERTED", "FAILED"], "example": "CONVERTED"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch bản ghi chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch bản ghi chuyển đổi thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserConvertResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/convert/{id}": {"get": {"tags": ["User - Convert"], "summary": "<PERSON><PERSON><PERSON> chi tiết bản ghi chuyển đổi", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một bản ghi chuyển đổi theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> của bản ghi chuyển đổi", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết bản ghi chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết bản ghi chuyển đổi thành công"}, "result": {"$ref": "#/components/schemas/UserConvertResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/convert-customers": {"get": {"tags": ["User - Convert Customer"], "summary": "<PERSON><PERSON><PERSON> danh sách khách hàng chuyển đổi", "description": "<PERSON><PERSON><PERSON> danh sách khách hàng chuyển đổi với phân trang và tìm kiếm", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON><PERSON> kiếm theo tên hoặc email khách hàng", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách khách hàng chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách khách hàng chuyển đổi thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserConvertCustomerResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Convert Customer"], "summary": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi mới", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi mới", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserConvertCustomerDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi thành công"}, "result": {"$ref": "#/components/schemas/UserConvertCustomerResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/convert-customers/{id}": {"get": {"tags": ["User - Convert Customer"], "summary": "<PERSON><PERSON><PERSON> chi tiết khách hàng chuyển đổi", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết khách hàng chuyển đổi bao gồm social links và custom fields", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của khách hàng chuyển đổi", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết khách hàng chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết khách hàng chuyển đổi thành công"}, "result": {"$ref": "#/components/schemas/UserConvertCustomerResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Convert Customer"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t kh<PERSON>ch hàng chuyển đổi", "description": "<PERSON><PERSON><PERSON> nhật thông tin khách hàng chuyển đổi theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của khách hàng chuyển đổi", "required": true, "schema": {"type": "integer", "example": 123}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserConvertCustomerDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t kh<PERSON>ch hàng chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t kh<PERSON>ch hàng chuyển đổi thành công"}, "result": {"$ref": "#/components/schemas/UserConvertCustomerResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Convert Customer"], "summary": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi theo ID (hard delete)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của khách hàng chuyển đổi", "required": true, "schema": {"type": "integer", "example": 123}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng chuyển đổi thành công"}, "result": {"type": "object", "nullable": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/customer-products": {"get": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng", "description": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng với phân trang và tìm kiếm", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> kiếm theo tên sản phẩm", "required": false, "schema": {"type": "string", "example": "iPhone"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái sản phẩm", "required": false, "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"], "example": "APPROVED"}}, {"name": "productType", "in": "query", "description": "<PERSON><PERSON><PERSON> theo lo<PERSON> sản phẩm", "required": false, "schema": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "example": "PHYSICAL"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách sản phẩm khách hàng thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerProductResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> sản phẩm khách hàng mới", "description": "<PERSON><PERSON><PERSON> sản phẩm khách hàng mới với thông tin cơ bản. Sản phẩm sẽ có trạng thái PENDING (chờ duyệt) sau khi tạo.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerProductDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> sản phẩm khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> sản phẩm khách hàng thành công"}, "result": {"$ref": "#/components/schemas/CustomerProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/customer-products/{id}": {"get": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm khách hàng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một sản phẩm khách hàng theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của sản ph<PERSON>m kh<PERSON>ch hàng", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm khách hàng thành công"}, "result": {"$ref": "#/components/schemas/CustomerProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "patch": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> sản phẩm kh<PERSON>ch hàng", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin sản phẩm khách hàng theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của sản ph<PERSON>m kh<PERSON>ch hàng", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm khách hàng thành công"}, "result": {"$ref": "#/components/schemas/CustomerProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Business Customer Product"], "summary": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m kh<PERSON>ch hàng", "description": "<PERSON><PERSON><PERSON> sản phẩm khách hàng theo ID (soft delete)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của sản ph<PERSON>m kh<PERSON>ch hàng", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> sản phẩm khách hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> sản phẩm khách hàng thành công"}, "result": {"type": "object", "nullable": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/custom-fields": {"get": {"tags": ["User - Business"], "summary": "<PERSON><PERSON><PERSON> danh sách trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> danh sách trường tùy chỉnh với phân trang và tìm kiếm", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> kiếm theo tên trường", "required": false, "schema": {"type": "string", "example": "email"}}, {"name": "fieldType", "in": "query", "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i trường", "required": false, "schema": {"type": "string", "enum": ["TEXT", "NUMBER", "EMAIL", "PHONE", "DATE", "BOOLEAN", "SELECT", "TEXTAREA"], "example": "TEXT"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách trường tùy chỉnh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách trường tùy chỉnh thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CustomFieldResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Business"], "summary": "Tạo trường tùy chỉnh mới", "description": "Tạo trường tùy chỉnh mới cho business", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomFieldDto"}}}}, "responses": {"201": {"description": "Tạo trường tùy chỉnh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "Tạo trường tùy chỉnh thành công"}, "result": {"$ref": "#/components/schemas/CustomFieldResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/physical/{id}": {"get": {"tags": ["User - Business Physical Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm vật lý", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của sản phẩm vật lý bao gồm variants và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm vật lý thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm vật lý thành công"}, "result": {"$ref": "#/components/schemas/CompletePhysicalProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Physical Product"], "summary": "<PERSON><PERSON><PERSON> nhật hoàn chỉnh sản phẩm vật lý", "description": "<PERSON><PERSON><PERSON> nhật toàn bộ thông tin sản phẩm vật lý bao gồm thông tin cơ bản, variants và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdatePhysicalProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật sản phẩm vật lý thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật sản phẩm vật lý thành công"}, "result": {"$ref": "#/components/schemas/CompletePhysicalProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/digital/{id}": {"get": {"tags": ["User - Business Digital Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm số", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của sản phẩm số bao gồm versions và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm số thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm số thành công"}, "result": {"$ref": "#/components/schemas/CompleteDigitalProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Digital Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t hoàn chỉnh sản phẩm số", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm số bao gồm thông tin cơ bản, versions và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateDigitalProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm số thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm số thành công"}, "result": {"$ref": "#/components/schemas/CompleteDigitalProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/custom-fields/{id}": {"get": {"tags": ["User - Business"], "summary": "<PERSON><PERSON><PERSON> chi tiết trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một trường tùy chỉnh theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của trường tùy chỉnh", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết trường tùy chỉnh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết trường tùy chỉnh thành công"}, "result": {"$ref": "#/components/schemas/CustomFieldResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business"], "summary": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh", "description": "<PERSON><PERSON><PERSON> nhật thông tin trường tùy chỉnh theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của trường tùy chỉnh", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomFieldDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật trường tùy chỉnh thành công"}, "result": {"$ref": "#/components/schemas/CustomFieldResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Business"], "summary": "<PERSON>óa trường tùy chỉnh", "description": "Xóa trường tùy chỉnh theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của trường tùy chỉnh", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON>óa trường tùy chỉnh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON>óa trường tùy chỉnh thành công"}, "result": {"type": "object", "nullable": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/v1/user/addresses": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> tất cả địa chỉ của user", "description": "<PERSON><PERSON><PERSON> danh sách tất cả địa chỉ của user hiệ<PERSON> tại (không phân trang)", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/UserAddressResponseDto"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ mới", "description": "Tạo một địa chỉ mới cho user hiện tại với hệ thống location service. Sử dụng UUID từ location service cho provinceUuid và wardUuid. Nếu isDefault = true, địa chỉ này sẽ trở thành địa chỉ mặc định", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserAddressDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> địa chỉ thành công"}, "result": {"$ref": "#/components/schemas/UserAddressResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/v1/user/addresses/{id}": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> chi tiết địa chỉ", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một địa chỉ cụ thể", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết địa chỉ thành công"}, "result": {"$ref": "#/components/schemas/UserAddressResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t địa chỉ", "description": "Cập nhật thông tin của một địa chỉ cụ thể. Chỉ có thể cập nhật địa chỉ của chính mình. Nếu isDefault = true, địa chỉ này sẽ trở thành địa chỉ mặc định", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserAddressDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật địa chỉ thành công"}, "result": {"$ref": "#/components/schemas/UserAddressResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ", "description": "<PERSON><PERSON><PERSON> một địa chỉ cụ thể (soft delete)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> địa chỉ thành công"}, "result": {"type": "boolean", "example": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/v1/user/addresses/{id}/set-default": {"put": {"tags": ["User - Address"], "summary": "Đặt địa chỉ làm mặc định", "description": "Đặt một địa chỉ cụ thể làm địa chỉ mặc định. Các địa chỉ mặc định khác sẽ tự động bị bỏ mặc định.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của địa chỉ", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Đặt địa chỉ mặc định thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Đặt địa chỉ mặc định thành công"}, "result": {"$ref": "#/components/schemas/UserAddressResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/v1/user/addresses/default/current": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> địa chỉ mặc định", "description": "<PERSON><PERSON><PERSON> địa chỉ mặc định của user hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> địa chỉ mặc định thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> địa chỉ mặc định thành công"}, "result": {"oneOf": [{"$ref": "#/components/schemas/UserAddressResponseDto"}, {"type": "null"}]}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/v1/user/addresses/paginated": {"get": {"tags": ["User - Address"], "summary": "<PERSON><PERSON><PERSON> danh sách địa chỉ với phân trang", "description": "<PERSON><PERSON><PERSON> danh sách địa chỉ của user với phân trang và bộ lọc", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (mặc định: 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng mỗi trang (mặc định: 10)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "addressType", "in": "query", "description": "Lo<PERSON>i địa chỉ", "required": false, "schema": {"type": "string", "enum": ["home", "office", "other"], "example": "home"}}, {"name": "isDefault", "in": "query", "description": "<PERSON><PERSON><PERSON> theo địa chỉ mặc định", "required": false, "schema": {"type": "boolean", "example": true}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách địa chỉ thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserAddressResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/event/{id}": {"get": {"tags": ["User - Business Event Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm sự kiện", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của sản phẩm sự kiện bao gồm tickets và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm sự kiện thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm sự kiện thành công"}, "result": {"$ref": "#/components/schemas/CompleteEventProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Event Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hoàn chỉnh sản phẩm sự kiện", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm sự kiện bao gồm thông tin cơ bản, tickets và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateEventProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm sự kiện thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm sự kiện thành công"}, "result": {"$ref": "#/components/schemas/CompleteEventProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/products/service/{id}": {"get": {"tags": ["User - Business Service Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm dịch vụ", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của sản phẩm dịch vụ bao gồm packages và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm dịch vụ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm dịch vụ thành công"}, "result": {"$ref": "#/components/schemas/CompleteServiceProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Service Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t hoàn chỉnh sản phẩm dịch vụ", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm dịch vụ bao gồm thông tin cơ bản, packages và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateServiceProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> sản phẩm dịch vụ thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> sản phẩm dịch vụ thành công"}, "result": {"$ref": "#/components/schemas/CompleteServiceProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/combo-products/{id}": {"get": {"tags": ["User - Business Combo Product"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm combo", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của sản phẩm combo bao gồm items và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm combo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết sản phẩm combo thành công"}, "result": {"$ref": "#/components/schemas/CompleteComboProductResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Business Combo Product"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t hoàn chỉnh sản phẩm combo", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t toàn bộ thông tin sản phẩm combo bao gồm thông tin cơ bản, items và images", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteUpdateComboProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm combo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm combo thành công"}, "result": {"$ref": "#/components/schemas/CompleteComboProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business/reports/overview": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON><PERSON> dữ liệu tổng quan báo cáo", "description": "API lấy dữ liệu tổng quan bao gồm tổng doanh thu, tổng đơn hàng, kh<PERSON><PERSON> hàng mới và so s<PERSON>h với kỳ trước", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dữ liệu tổng quan thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> dữ liệu tổng quan thành công"}, "result": {"$ref": "#/components/schemas/ReportOverviewResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business/reports/sales-chart": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ doanh thu", "description": "API lấy dữ liệu biểu đồ doanh thu theo thời gian. Hệ thống tự động nhóm dữ liệu dựa trên khoảng thời gian", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ doanh thu thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ doanh thu thành công"}, "result": {"$ref": "#/components/schemas/SalesChartResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/business/reports/orders-chart": {"get": {"tags": ["User - Business Report"], "summary": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ đơn hàng", "description": "API lấy dữ liệu biểu đồ đơn hàng theo trạng thái và thời gian", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kế<PERSON> th<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> dữ liệu biểu đồ đơn hàng thành công"}, "result": {"$ref": "#/components/schemas/OrdersChartResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/payment-qr/custom": {"get": {"tags": ["User - Payment QR"], "summary": "Lấy QR Code tùy chỉnh", "description": "Tạo URL QR Code thanh toán với nội dung tùy chỉnh", "security": [{"bearerAuth": []}], "parameters": [{"name": "amount", "in": "query", "description": "<PERSON><PERSON> tiền cần thanh toán", "required": true, "schema": {"type": "number", "example": 100000}}, {"name": "description", "in": "query", "description": "<PERSON><PERSON><PERSON> dung chuyển k<PERSON>n", "required": true, "schema": {"type": "string", "example": "<PERSON>h toan don hang"}}], "responses": {"200": {"description": "Lấy QR Code tùy chỉnh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Lấy QR Code tùy chỉnh thành công"}, "result": {"type": "object", "properties": {"qrCodeUrl": {"type": "string", "example": "https://qr.sepay.vn/img?bank=970422&acc=**********&template=compact&amount=100000.00&des=Thanh%20toan%20don%20hang"}, "description": {"type": "string", "example": "<PERSON>h toan don hang"}, "amount": {"type": "number", "example": 100000}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/converts": {"get": {"tags": ["User - Convert"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch bản ghi chuyển đổi", "description": "<PERSON><PERSON><PERSON> danh sách bản ghi chuyển đổi khách hàng với phân trang và tìm kiếm. Hỗ trợ lọc theo trạng thái và sắp xếp theo nhiều tiêu chí.", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON>m kiếm theo tên hoặc thông tin khách hàng", "required": false, "schema": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái chuyển đổi", "required": false, "schema": {"type": "string", "enum": ["PENDING", "CONVERTED", "FAILED"], "example": "CONVERTED"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch bản ghi chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh s<PERSON>ch bản ghi chuyển đổi thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserConvertListItemDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/converts/detail/{id}": {"get": {"tags": ["User - Convert"], "summary": "<PERSON><PERSON><PERSON> chi tiết bản ghi chuyển đổi với thông tin khách hàng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một bản ghi chuyển đổi bao gồm thông tin khách hàng, lịch sử tương tác và metadata", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> của bản ghi chuyển đổi", "required": true, "schema": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết bản ghi chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết bản ghi chuyển đổi thành công"}, "result": {"$ref": "#/components/schemas/UserConvertResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/{id}/print": {"get": {"tags": ["User - Order Tracking"], "summary": "In đơn hàng", "description": "Tự động xác định đơn vị vận chuyển từ đơn hàng và in theo API của carrier đó. GHN trả về token, GHTK trả về file PDF trực tiếp.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của đơn hàng", "required": true, "schema": {"type": "string", "example": "24"}}, {"name": "paperSize", "in": "query", "description": "<PERSON><PERSON><PERSON> giấy in (chỉ áp dụng cho GHTK)", "required": false, "schema": {"type": "string", "enum": ["A5", "A6"], "example": "A6"}}, {"name": "orientation", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> (chỉ áp dụng cho GHTK)", "required": false, "schema": {"type": "string", "enum": ["portrait", "landscape"], "example": "portrait"}}], "responses": {"200": {"description": "In đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "In đơn hàng thành công"}, "data": {"$ref": "#/components/schemas/PrintOrderResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/orders/calculate-shipping": {"post": {"tags": ["User - Order Tracking"], "summary": "<PERSON><PERSON><PERSON> phí vận chuyển", "description": "<PERSON><PERSON>h phí vận chuyển cho đơn hàng từ các nhà cung cấp vận chuyển", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateShippingFeeRequestDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON>h phí vận chuyển thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>h phí vận chuyển thành công"}, "data": {"$ref": "#/components/schemas/CalculateShippingFeeResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON> liệu đầu vào không hợp lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 400}, "message": {"type": "string", "example": "<PERSON><PERSON> liệu đầu vào không hợp lệ"}, "errors": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string", "example": "name"}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> sản phẩm không được để trống"}}}}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON> xác thực hoặc token không hợp lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 404}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên"}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 500}, "message": {"type": "string", "example": "Lỗi máy chủ nội bộ"}}}}}}}, "schemas": {"PaginationMeta": {"type": "object", "properties": {"totalItems": {"type": "integer", "example": 100}, "totalPages": {"type": "integer", "example": 10}, "currentPage": {"type": "integer", "example": 1}, "itemsPerPage": {"type": "integer", "example": 10}, "hasNextPage": {"type": "boolean", "example": true}, "hasPreviousPage": {"type": "boolean", "example": false}}}, "CreateDraftOrderDto": {"type": "object", "properties": {"userShopAddressId": {"type": "integer", "description": "ID địa chỉ shop của người dùng", "example": 1}, "deliveryAddress": {"type": "object", "description": "<PERSON><PERSON><PERSON><PERSON> tin địa chỉ giao hàng", "properties": {"customerName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerPhone": {"type": "string", "example": "**********"}, "customerAddress": {"type": "string", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "provinceId": {"type": "integer", "example": 1}, "wardId": {"type": "integer", "example": 1}}}, "products": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch sản phẩm trong đơn hàng", "items": {"type": "object", "properties": {"customerProductId": {"type": "integer", "example": 1}, "quantity": {"type": "integer", "example": 2}, "price": {"type": "number", "example": 100000}}}}, "logisticInfo": {"type": "object", "description": "Thông tin vận chuyển", "properties": {"shippingProvider": {"type": "string", "enum": ["GHN", "GHTK", "AHAMOVE"], "example": "GHN"}, "serviceTypeId": {"type": "integer", "example": 1}, "shippingFee": {"type": "number", "example": 30000}}}, "paymentInfo": {"type": "object", "description": "Thông tin thanh toán", "properties": {"paymentMethod": {"type": "string", "enum": ["COD", "BANK_TRANSFER", "CREDIT_CARD"], "example": "COD"}}}}, "required": ["userShopAddressId", "deliveryAddress", "products", "logisticInfo"]}, "UserOrderResponseDto": {"type": "object", "properties": {"id": {"type": "string", "example": "24"}, "orderCode": {"type": "string", "example": "ORD-001"}, "status": {"type": "string", "enum": ["DRAFT", "CONFIRMED", "SHIPPED", "DELIVERED", "CANCELLED", "RETURNED"], "example": "CONFIRMED"}, "shippingStatus": {"type": "string", "enum": ["PENDING", "PICKED_UP", "IN_TRANSIT", "DELIVERED", "FAILED", "RETURNED"], "example": "PENDING"}, "totalAmount": {"type": "number", "example": 230000}, "shippingFee": {"type": "number", "example": 30000}, "customerName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerPhone": {"type": "string", "example": "**********"}, "customerAddress": {"type": "string", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "UserOrderListItemDto": {"type": "object", "properties": {"id": {"type": "string", "example": "24"}, "orderCode": {"type": "string", "example": "ORD-001"}, "status": {"type": "string", "enum": ["DRAFT", "CONFIRMED", "SHIPPED", "DELIVERED", "CANCELLED", "RETURNED"], "example": "CONFIRMED"}, "shippingStatus": {"type": "string", "enum": ["PENDING", "PICKED_UP", "IN_TRANSIT", "DELIVERED", "FAILED", "RETURNED"], "example": "PENDING"}, "totalAmount": {"type": "number", "example": 230000}, "customerName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerPhone": {"type": "string", "example": "**********"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "UpdateUserOrderDto": {"type": "object", "properties": {"customerName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerPhone": {"type": "string", "example": "0907654321"}, "customerAddress": {"type": "string", "example": "456 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 2, TP.<PERSON>M"}, "notes": {"type": "string", "example": "<PERSON><PERSON> chú đặc biệt"}}}, "UserOrderStatusResponseDto": {"type": "object", "properties": {"totalOrders": {"type": "integer", "example": 100}, "statusCounts": {"type": "object", "properties": {"DRAFT": {"type": "integer", "example": 5}, "CONFIRMED": {"type": "integer", "example": 30}, "SHIPPED": {"type": "integer", "example": 40}, "DELIVERED": {"type": "integer", "example": 20}, "CANCELLED": {"type": "integer", "example": 3}, "RETURNED": {"type": "integer", "example": 2}}}, "shippingStatusCounts": {"type": "object", "properties": {"PENDING": {"type": "integer", "example": 10}, "PICKED_UP": {"type": "integer", "example": 15}, "IN_TRANSIT": {"type": "integer", "example": 25}, "DELIVERED": {"type": "integer", "example": 45}, "FAILED": {"type": "integer", "example": 3}, "RETURNED": {"type": "integer", "example": 2}}}}}, "TrackOrderResponseDto": {"type": "object", "properties": {"orderId": {"type": "string", "example": "24"}, "orderCode": {"type": "string", "example": "ORD-001"}, "trackingCode": {"type": "string", "example": "GHN123456789"}, "shippingProvider": {"type": "string", "example": "GHN"}, "currentStatus": {"type": "string", "example": "IN_TRANSIT"}, "trackingHistory": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "example": "PICKED_UP"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> l<PERSON>y"}, "timestamp": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00.000Z"}, "location": {"type": "string", "example": "Kho GHN Quận 1"}}}}}}, "CreateCustomerProductDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "iPhone 15 Pro Max"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> thông minh cao cấp"}, "productType": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "description": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "example": "PHYSICAL"}, "price": {"type": "number", "description": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "example": 29990000}, "tags": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>", "example": ["smartphone", "apple", "premium"]}, "inventoryManagement": {"type": "boolean", "description": "<PERSON><PERSON> quản lý tồn kho hay không", "example": true}}, "required": ["name", "productType", "price"]}, "UpdateCustomerProductDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "iPhone 15 Pro Max Updated"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i thông minh cao cấp - phiên bản cập nhật"}, "price": {"type": "number", "description": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "example": 28990000}, "tags": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>", "example": ["smartphone", "apple", "premium", "updated"]}, "inventoryManagement": {"type": "boolean", "description": "<PERSON><PERSON> quản lý tồn kho hay không", "example": true}}}, "CustomerProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "iPhone 15 Pro Max"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> thông minh cao cấp"}, "productType": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "example": "PHYSICAL"}, "price": {"type": "number", "example": 29990000}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"], "example": "APPROVED"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["smartphone", "apple", "premium"]}, "inventoryManagement": {"type": "boolean", "example": true}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "CompleteUpdatePhysicalProductDto": {"type": "object", "properties": {"name": {"type": "string", "example": "iPhone 15 Pro"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> thông minh cao cấp"}, "price": {"type": "number", "example": 25000000}, "sku": {"type": "string", "example": "IP15P-128GB-BLK"}, "barcode": {"type": "string", "example": "**********123"}, "variants": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "128GB - <PERSON><PERSON>"}, "price": {"type": "number", "example": 25000000}, "sku": {"type": "string", "example": "IP15P-128GB-BLK"}}}}}}, "CompletePhysicalProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "iPhone 15 Pro"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> thông minh cao cấp"}, "price": {"type": "number", "example": 25000000}, "productType": {"type": "string", "example": "PHYSICAL"}, "sku": {"type": "string", "example": "IP15P-128GB-BLK"}, "barcode": {"type": "string", "example": "**********123"}, "variants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "128GB - <PERSON><PERSON>"}, "price": {"type": "number", "example": 25000000}, "sku": {"type": "string", "example": "IP15P-128GB-BLK"}}}}, "images": {"type": "array", "items": {"type": "string"}, "example": ["https://example.com/image1.jpg"]}}}, "CompleteUpdateDigitalProductDto": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> l<PERSON> trình"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> họ<PERSON> lập trình từ cơ bản đến nâng cao"}, "price": {"type": "number", "example": 500000}, "digitalProductType": {"type": "string", "example": "COURSE"}, "accessLink": {"type": "string", "example": "https://course.example.com"}}}, "CompleteDigitalProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> l<PERSON> trình"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> họ<PERSON> lập trình từ cơ bản đến nâng cao"}, "price": {"type": "number", "example": 500000}, "productType": {"type": "string", "example": "DIGITAL"}, "digitalProductType": {"type": "string", "example": "COURSE"}, "accessLink": {"type": "string", "example": "https://course.example.com"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "version": {"type": "string", "example": "v1.0"}, "downloadUrl": {"type": "string", "example": "https://download.example.com/v1.0"}}}}}}, "CancelOrderRequestDto": {"type": "object", "properties": {"reason": {"type": "string", "description": "Lý do hủy đơn hàng", "example": "<PERSON><PERSON><PERSON><PERSON> hàng thay đổi ý định"}}, "required": ["reason"]}, "CancelOrderResponseDto": {"type": "object", "properties": {"orderId": {"type": "string", "example": "24"}, "status": {"type": "string", "example": "CANCELLED"}, "cancelledAt": {"type": "string", "format": "date-time", "example": "2024-01-01T12:00:00.000Z"}, "reason": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> hàng thay đổi ý định"}}}, "ReturnOrderRequestDto": {"type": "object", "properties": {"reason": {"type": "string", "description": "Lý do hoàn trả", "example": "<PERSON><PERSON><PERSON> phẩm bị lỗi"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả chi tiết", "example": "<PERSON><PERSON><PERSON> phẩm bị hỏng khi nhận hàng"}}, "required": ["reason"]}, "ReturnOrderResponseDto": {"type": "object", "properties": {"orderId": {"type": "string", "example": "24"}, "status": {"type": "string", "example": "RETURNED"}, "returnedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T12:00:00.000Z"}, "reason": {"type": "string", "example": "<PERSON><PERSON><PERSON> phẩm bị lỗi"}}}, "DeliveryAgainOrderRequestDto": {"type": "object", "properties": {"reason": {"type": "string", "description": "Lý do giao lại", "example": "<PERSON><PERSON><PERSON><PERSON> hàng không có mặt"}, "newDeliveryDate": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON> giao mới", "example": "2024-01-15"}}, "required": ["reason"]}, "DeliveryAgainOrderResponseDto": {"type": "object", "properties": {"orderId": {"type": "string", "example": "24"}, "status": {"type": "string", "example": "DELIVERY_AGAIN"}, "newDeliveryDate": {"type": "string", "format": "date", "example": "2024-01-15"}}}, "BulkDeleteUserOrderDto": {"type": "object", "properties": {"orderIds": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> đơn hàng cần xóa", "example": ["24", "25", "26"]}}, "required": ["orderIds"]}, "BulkDeleteUserOrderResponseDto": {"type": "object", "properties": {"deletedCount": {"type": "integer", "example": 3}, "failedIds": {"type": "array", "items": {"type": "string"}, "example": []}}}, "CreateCustomFieldDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> tr<PERSON>", "example": "<PERSON><PERSON>"}, "fieldKey": {"type": "string", "description": "Key của trường (unique)", "example": "customer_email"}, "fieldType": {"type": "string", "enum": ["TEXT", "NUMBER", "EMAIL", "PHONE", "DATE", "BOOLEAN", "SELECT", "TEXTAREA"], "description": "Loại trường", "example": "EMAIL"}, "isRequired": {"type": "boolean", "description": "<PERSON><PERSON> b<PERSON> buộc hay không", "example": true}, "defaultValue": {"type": "string", "description": "<PERSON><PERSON><PERSON> trị mặc định", "example": ""}, "options": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON><PERSON><PERSON> (cho SELECT)", "example": ["Option 1", "Option 2"]}}, "required": ["name", "<PERSON><PERSON><PERSON>", "fieldType"]}, "UpdateCustomFieldDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> tr<PERSON>", "example": "<PERSON><PERSON> hàng cập nh<PERSON>t"}, "isRequired": {"type": "boolean", "description": "<PERSON><PERSON> b<PERSON> buộc hay không", "example": false}, "defaultValue": {"type": "string", "description": "<PERSON><PERSON><PERSON> trị mặc định", "example": "<EMAIL>"}, "options": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON><PERSON><PERSON> (cho SELECT)", "example": ["Option 1", "Option 2", "Option 3"]}}}, "CustomFieldResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON><PERSON>"}, "fieldKey": {"type": "string", "example": "customer_email"}, "fieldType": {"type": "string", "enum": ["TEXT", "NUMBER", "EMAIL", "PHONE", "DATE", "BOOLEAN", "SELECT", "TEXTAREA"], "example": "EMAIL"}, "isRequired": {"type": "boolean", "example": true}, "defaultValue": {"type": "string", "example": ""}, "options": {"type": "array", "items": {"type": "string"}, "example": ["Option 1", "Option 2"]}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "CreateUserConvertCustomerDto": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> h<PERSON>ng", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}}}, "UserConvertCustomerResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "**********"}, "address": {"type": "string", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCKED"], "example": "ACTIVE"}, "socialLinks": {"type": "array", "items": {"type": "object", "properties": {"platform": {"type": "string", "example": "facebook"}, "url": {"type": "string", "example": "https://facebook.com/nguyenvana"}}}}, "customFields": {"type": "object", "additionalProperties": true, "example": {"company": "ABC Corp", "position": "Manager"}}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}}}, "CreateUserAddressDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> chỉ", "example": "Nhà riêng"}, "address": {"type": "string", "description": "Đ<PERSON><PERSON> chỉ chi tiết", "example": "123 Đường ABC, Phường XYZ"}, "provinceUuid": {"type": "string", "description": "UUID tỉnh/thành phố", "example": "550e8400-e29b-41d4-a716-************"}, "wardUuid": {"type": "string", "description": "UUID phường/xã", "example": "550e8400-e29b-41d4-a716-************"}, "addressType": {"type": "string", "enum": ["home", "office", "other"], "description": "Lo<PERSON>i địa chỉ", "example": "home"}, "isDefault": {"type": "boolean", "description": "<PERSON><PERSON> phải địa chỉ mặc định", "example": false}}, "required": ["name", "address", "provinceUuid", "wardUuid"]}, "UpdateUserAddressDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> chỉ", "example": "<PERSON><PERSON><PERSON> ri<PERSON><PERSON> cập nh<PERSON>t"}, "address": {"type": "string", "description": "Đ<PERSON><PERSON> chỉ chi tiết", "example": "456 Đư<PERSON>ng XYZ, Phường ABC"}, "provinceUuid": {"type": "string", "description": "UUID tỉnh/thành phố", "example": "550e8400-e29b-41d4-a716-************"}, "wardUuid": {"type": "string", "description": "UUID phường/xã", "example": "550e8400-e29b-41d4-a716-************"}, "addressType": {"type": "string", "enum": ["home", "office", "other"], "description": "Lo<PERSON>i địa chỉ", "example": "office"}, "isDefault": {"type": "boolean", "description": "<PERSON><PERSON> phải địa chỉ mặc định", "example": true}}}, "UserAddressResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Nhà riêng"}, "address": {"type": "string", "example": "123 Đường ABC, Phường XYZ"}, "provinceUuid": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "wardUuid": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "provinceName": {"type": "string", "example": "T<PERSON><PERSON> <PERSON><PERSON>"}, "wardName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "addressType": {"type": "string", "enum": ["home", "office", "other"], "example": "home"}, "isDefault": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "UserShopAddressV2Dto": {"type": "object", "required": ["shopName", "shopAddress", "shopPhone", "provinceId", "wardId"], "properties": {"shopName": {"type": "string", "description": "Tên shop", "example": "Shop ABC"}, "shopAddress": {"type": "string", "description": "Địa chỉ shop", "example": "123 Đường XYZ"}, "shopPhone": {"type": "string", "description": "Số điện thoại shop", "example": "**********"}, "provinceId": {"type": "string", "description": "UUID tỉnh/thành phố", "example": "550e8400-e29b-41d4-a716-************"}, "wardId": {"type": "string", "description": "UUID phường/xã", "example": "550e8400-e29b-41d4-a716-************"}, "isDefault": {"type": "boolean", "description": "Đặt làm shop mặc định", "example": false}}}, "UpdateUserShopAddressV2Dto": {"type": "object", "properties": {"shopName": {"type": "string", "description": "Tên shop", "example": "Shop ABC"}, "shopAddress": {"type": "string", "description": "Địa chỉ shop", "example": "123 Đường XYZ"}, "shopPhone": {"type": "string", "description": "Số điện thoại shop", "example": "**********"}, "provinceId": {"type": "string", "description": "UUID tỉnh/thành phố", "example": "550e8400-e29b-41d4-a716-************"}, "wardId": {"type": "string", "description": "UUID phường/xã", "example": "550e8400-e29b-41d4-a716-************"}}}, "UserShopAddressV2ResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "shopName": {"type": "string", "example": "Shop ABC"}, "shopAddress": {"type": "string", "example": "123 Đường XYZ"}, "shopPhone": {"type": "string", "example": "**********"}, "provinceId": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "wardId": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "isDefault": {"type": "boolean", "example": true}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}}}, "DeleteMultipleShopsV2Dto": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "Danh sách ID shop cần xóa", "example": [1, 2, 3]}}}, "CompleteUpdateEventProductDto": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON> thảo công nghệ"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> thảo về công nghệ mới nhất"}, "price": {"type": "number", "example": 200000}, "eventDate": {"type": "string", "format": "date-time", "example": "2024-12-25T10:00:00Z"}, "location": {"type": "string", "example": "Trung tâm hội nghị <PERSON>"}}}, "CompleteEventProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON> thảo công nghệ"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> thảo về công nghệ mới nhất"}, "price": {"type": "number", "example": 200000}, "productType": {"type": "string", "example": "EVENT"}, "eventDate": {"type": "string", "format": "date-time", "example": "2024-12-25T10:00:00Z"}, "location": {"type": "string", "example": "Trung tâm hội nghị <PERSON>"}, "tickets": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Vé VIP"}, "price": {"type": "number", "example": 500000}, "quantity": {"type": "integer", "example": 100}}}}}}, "CompleteUpdateServiceProductDto": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON> v<PERSON> tư vấn"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> v<PERSON> tư vấn chuy<PERSON>"}, "price": {"type": "number", "example": 1000000}}}, "CompleteServiceProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON> v<PERSON> tư vấn"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> v<PERSON> tư vấn chuy<PERSON>"}, "price": {"type": "number", "example": 1000000}, "productType": {"type": "string", "example": "SERVICE"}, "servicePackages": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON> bản"}, "price": {"type": "number", "example": 500000}, "duration": {"type": "string", "example": "1 tháng"}}}}}}, "CompleteUpdateComboProductDto": {"type": "object", "properties": {"name": {"type": "string", "example": "Combo sản phẩm A + B"}, "description": {"type": "string", "example": "Combo bao gồm sản phẩm A và B với giá ưu đãi"}, "price": {"type": "number", "example": 800000}, "maxQuantity": {"type": "integer", "example": 100}}}, "CompleteComboProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "Combo sản phẩm A + B"}, "description": {"type": "string", "example": "Combo bao gồm sản phẩm A và B với giá ưu đãi"}, "price": {"type": "number", "example": 800000}, "productType": {"type": "string", "example": "COMBO"}, "maxQuantity": {"type": "integer", "example": 100}, "totalItems": {"type": "integer", "example": 2}, "comboItems": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer", "example": 1}, "productName": {"type": "string", "example": "<PERSON><PERSON><PERSON> phẩm A"}, "quantity": {"type": "integer", "example": 1}, "price": {"type": "number", "example": 500000}}}}}}, "ReportOverviewResponseDto": {"type": "object", "properties": {"totalRevenue": {"type": "number", "example": 50000000}, "totalOrders": {"type": "integer", "example": 150}, "newCustomers": {"type": "integer", "example": 25}, "revenueGrowth": {"type": "number", "example": 15.5}, "orderGrowth": {"type": "number", "example": 10.2}, "customerGrowth": {"type": "number", "example": 8.7}}}, "SalesChartResponseDto": {"type": "object", "properties": {"period": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly"], "example": "daily"}, "data": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "example": "2024-01-15"}, "revenue": {"type": "number", "example": 1500000}, "orders": {"type": "integer", "example": 5}}}}}}, "OrdersChartResponseDto": {"type": "object", "properties": {"chartData": {"type": "array", "items": {"type": "object", "properties": {"period": {"type": "string", "example": "2024-01-01"}, "confirmed": {"type": "integer", "example": 10}, "delivered": {"type": "integer", "example": 8}, "cancelled": {"type": "integer", "example": 1}}}}, "totalOrders": {"type": "integer", "example": 150}}}, "CreateEntityHasMediaDto": {"type": "object", "required": ["entityType", "entityId", "mediaId"], "properties": {"entityType": {"type": "string", "enum": ["product", "physicalVarial", "ticketVarial", "version", "combo", "plan"], "description": "Loại entity", "example": "product"}, "entityId": {"type": "integer", "description": "ID của entity", "example": 123}, "mediaId": {"type": "integer", "description": "ID của media", "example": 456}}}, "UpdateEntityHasMediaDto": {"type": "object", "properties": {"isPrimary": {"type": "boolean", "description": "Có phải media chính", "example": false}}}, "EntityHasMediaResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "mediaId": {"type": "integer", "example": 1}, "entityType": {"type": "string", "example": "product"}, "entityId": {"type": "integer", "example": 1}, "isPrimary": {"type": "boolean", "example": true}, "mediaUrl": {"type": "string", "example": "https://example.com/media/image.jpg"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "UserConvertListItemDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}, "customerName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerEmail": {"type": "string", "example": "<EMAIL>"}, "status": {"type": "string", "enum": ["PENDING", "CONVERTED", "FAILED"], "example": "CONVERTED"}, "convertedAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "source": {"type": "string", "example": "Website"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}}}, "UserConvertResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "customerName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerEmail": {"type": "string", "example": "<EMAIL>"}, "status": {"type": "string", "enum": ["PENDING", "CONVERTED", "FAILED"], "example": "CONVERTED"}, "convertedAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}}}, "SimpleCreateCustomerProductDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "iPhone 15 Pro Max"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> thông minh cao cấp"}, "productType": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "description": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "example": "PHYSICAL"}}, "required": ["name", "description", "productType"]}, "SimpleCustomerProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 123}, "name": {"type": "string", "example": "iPhone 15 Pro"}, "price": {"type": "number", "example": 25000000}, "productType": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "example": "PHYSICAL"}, "status": {"type": "string", "example": "APPROVED"}, "thumbnailUrl": {"type": "string", "example": "https://example.com/thumbnail.jpg"}}}, "TrackingApiResponseDto": {"type": "object", "properties": {"orderId": {"type": "integer", "example": 1}, "trackingCode": {"type": "string", "example": "GHN123456789"}, "status": {"type": "string", "enum": ["PENDING", "PICKED_UP", "IN_TRANSIT", "DELIVERED", "FAILED", "RETURNED"], "example": "IN_TRANSIT"}, "currentLocation": {"type": "string", "example": "Kho phân loại Hà Nội"}, "estimatedDelivery": {"type": "string", "format": "date-time", "example": "2024-01-20T15:00:00Z"}, "trackingHistory": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "example": "PICKED_UP"}, "location": {"type": "string", "example": "<PERSON><PERSON> hàng"}, "timestamp": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "note": {"type": "string", "example": "<PERSON><PERSON> lấy hàng thành công"}}}}}}, "PrintOrderResponseDto": {"type": "object", "properties": {"carrier": {"type": "string", "example": "GHN"}, "orderId": {"type": "string", "example": "24"}, "printToken": {"type": "string", "description": "Token để in (GHN)", "example": "abc123<PERSON><PERSON>"}, "pdfUrl": {"type": "string", "description": "URL file PDF (GHTK)", "example": "https://example.com/print.pdf"}, "printUrl": {"type": "string", "description": "URL để in trực tiếp", "example": "https://dev-online-gateway.ghn.vn/a5/public-api/printA5?token=abc123token"}}}, "CalculateShippingFeeRequestDto": {"type": "object", "properties": {"fromAddress": {"type": "object", "properties": {"provinceId": {"type": "integer", "example": 1}, "districtId": {"type": "integer", "example": 1}, "wardId": {"type": "integer", "example": 1}}}, "toAddress": {"type": "object", "properties": {"provinceId": {"type": "integer", "example": 2}, "districtId": {"type": "integer", "example": 2}, "wardId": {"type": "integer", "example": 2}}}, "weight": {"type": "number", "description": "Tr<PERSON><PERSON> l<PERSON> (gram)", "example": 500}, "length": {"type": "number", "description": "<PERSON><PERSON><PERSON> dài (cm)", "example": 20}, "width": {"type": "number", "description": "<PERSON><PERSON>u rộng (cm)", "example": 15}, "height": {"type": "number", "description": "<PERSON><PERSON><PERSON> cao (cm)", "example": 10}, "codAmount": {"type": "number", "description": "<PERSON><PERSON> tiền thu hộ", "example": 100000}, "insuranceValue": {"type": "number", "description": "<PERSON><PERSON><PERSON> trị b<PERSON><PERSON>", "example": 100000}}, "required": ["fromAddress", "to<PERSON><PERSON><PERSON>", "weight"]}, "CalculateShippingFeeResponseDto": {"type": "object", "properties": {"carriers": {"type": "array", "items": {"type": "object", "properties": {"carrier": {"type": "string", "example": "GHN"}, "serviceName": {"type": "string", "example": "<PERSON><PERSON><PERSON> hàng ti<PERSON><PERSON>"}, "serviceId": {"type": "integer", "example": 53320}, "fee": {"type": "number", "example": 25000}, "estimatedDelivery": {"type": "string", "example": "2-3 ng<PERSON>y"}, "available": {"type": "boolean", "example": true}}}}, "recommendedCarrier": {"type": "string", "example": "GHN"}}}, "ProvinceResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của tỉnh/thành phố (UUID)", "example": "550e8400-e29b-41d4-a716-************"}, "provinceName": {"type": "string", "description": "Tên tỉnh/thành phố", "example": "<PERSON><PERSON>"}, "provinceId": {"type": "integer", "description": "ID tỉnh/thành phố", "example": 1}, "extensionNames": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> sách tên mở rộng", "example": ["<PERSON><PERSON><PERSON><PERSON> p<PERSON>ố <PERSON> Nộ<PERSON>", "<PERSON><PERSON>"]}}}, "WardResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "uuid": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "provinceId": {"type": "integer", "example": 1}}}, "UserAddressV2ResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Địa chỉ nhà"}, "address": {"type": "string", "example": "123 Đường ABC"}, "provinceUuid": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "wardUuid": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "isDefault": {"type": "boolean", "example": true}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}}}, "CreateUserAddressV2Dto": {"type": "object", "required": ["name", "address", "provinceUuid", "wardUuid"], "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> chỉ", "example": "Địa chỉ nhà"}, "address": {"type": "string", "description": "Đ<PERSON><PERSON> chỉ chi tiết", "example": "123 Đường ABC"}, "provinceUuid": {"type": "string", "description": "UUID tỉnh/thành phố", "example": "550e8400-e29b-41d4-a716-************"}, "wardUuid": {"type": "string", "description": "UUID phường/xã", "example": "550e8400-e29b-41d4-a716-************"}, "isDefault": {"type": "boolean", "description": "Đặt làm địa chỉ mặc định", "example": false}}}, "UpdateUserAddressV2Dto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> chỉ", "example": "Địa chỉ nhà"}, "address": {"type": "string", "description": "Đ<PERSON><PERSON> chỉ chi tiết", "example": "123 Đường ABC"}, "provinceUuid": {"type": "string", "description": "UUID tỉnh/thành phố", "example": "550e8400-e29b-41d4-a716-************"}, "wardUuid": {"type": "string", "description": "UUID phường/xã", "example": "550e8400-e29b-41d4-a716-************"}}}, "BulkCreateCustomerProductDto": {"type": "object", "required": ["products"], "properties": {"products": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON> phẩm A"}, "description": {"type": "string", "example": "<PERSON><PERSON> tả sản phẩm A"}, "price": {"type": "number", "example": 100000}, "productType": {"type": "string", "enum": ["PHYSICAL", "DIGITAL", "EVENT", "SERVICE", "COMBO"], "example": "PHYSICAL"}}}, "maxItems": 200, "description": "<PERSON><PERSON> s<PERSON>ch sản phẩm cần tạo (tối đa 200)"}}}, "BulkCreateCustomerProductResponseDto": {"type": "object", "properties": {"jobId": {"type": "string", "example": "job_123456"}, "totalItems": {"type": "integer", "example": 50}, "status": {"type": "string", "example": "PROCESSING"}}}, "BulkDeleteCustomerProductDto": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON>ch <PERSON> sản phẩm cần xóa", "example": [1, 2, 3, 4, 5]}}}, "BulkDeleteCustomerProductResponseDto": {"type": "object", "properties": {"deletedCount": {"type": "integer", "example": 5}, "failedIds": {"type": "array", "items": {"type": "integer"}, "example": []}}}, "ImportCustomerProductsResponseDto": {"type": "object", "properties": {"jobId": {"type": "string", "example": "job_123456"}, "fileName": {"type": "string", "example": "products.xlsx"}, "status": {"type": "string", "example": "PROCESSING"}}}, "TopProductsResponseDto": {"type": "object", "properties": {"products": {"type": "array", "items": {"type": "object", "properties": {"productId": {"type": "integer", "example": 123}, "productName": {"type": "string", "example": "iPhone 15 Pro"}, "totalSold": {"type": "integer", "example": 50}, "totalRevenue": {"type": "number", "example": 1250000000}, "rank": {"type": "integer", "example": 1}}}}}}, "CustomerAnalyticsResponseDto": {"type": "object", "properties": {"newCustomers": {"type": "integer", "example": 25}, "returningCustomers": {"type": "integer", "example": 75}, "conversionRate": {"type": "number", "example": 3.5}, "averageOrderValue": {"type": "number", "example": 333333}, "customerLifetimeValue": {"type": "number", "example": 1500000}}}, "UserConvertCustomerListItemDto": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "**********"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCKED"], "example": "ACTIVE"}, "createdAt": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00Z"}}}, "UpdateUserConvertCustomerDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> h<PERSON>ng", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "BLOCKED"], "description": "<PERSON>r<PERSON><PERSON> thái kh<PERSON>ch hàng", "example": "ACTIVE"}}}}}}