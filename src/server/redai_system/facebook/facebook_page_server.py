#!/usr/bin/env python3
"""
Facebook Page MCP Server

MCP Server cho Facebook Page API sử dụng FastMCP framework với HTTP transport.
Cung cấp các tools để quản lý Facebook Pages, posts, photos, events, và insights.

Author: RedAI Team
Date: 2024-12-28
"""

import asyncio
import json
import os
import sys
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
sys.path.insert(0, project_root)

from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request

# Facebook Business SDK imports
try:
    from facebook_business.api import FacebookAdsApi
    from facebook_business.adobjects.page import Page
    from facebook_business.adobjects.pagepost import PagePost
    from facebook_business.adobjects.photo import Photo
    from facebook_business.adobjects.album import Album
    from facebook_business.adobjects.event import Event
    from facebook_business.adobjects.advideo import AdVideo
    from facebook_business.adobjects.user import User
    from facebook_business.exceptions import FacebookRequestError
except ImportError as e:
    print(f"❌ Lỗi import Facebook Business SDK: {e}")
    print("💡 Cài đặt: pip install facebook-business")
    sys.exit(1)

import httpx

# Cấu hình server
HTTP_HOST = os.getenv("FACEBOOK_PAGE_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("FACEBOOK_PAGE_HTTP_PORT", "8023"))
HTTP_PATH = os.getenv("FACEBOOK_PAGE_HTTP_PATH", "/mcp")

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers
    
    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
        return None
    except Exception:
        return None

class FacebookPageClient:
    """Client để tương tác với Facebook Page API"""
    
    def __init__(self):
        self.api = None
    
    def _get_api(self, access_token: str):
        """Tạo Facebook API client với access token"""
        try:
            # Initialize Facebook API
            app_id = os.getenv('FACEBOOK_APP_ID', '')
            app_secret = os.getenv('FACEBOOK_APP_SECRET', '')
            
            api = FacebookAdsApi.init(app_id, app_secret, access_token)
            self.api = api
            return api
        except Exception as e:
            raise Exception(f"Lỗi tạo Facebook API client: {str(e)}")
    
    async def get_page_info(self, access_token: str, page_id: str) -> Dict:
        """Lấy thông tin chi tiết của page"""
        self._get_api(access_token)
        
        try:
            page = Page(page_id)
            page_info = page.api_get(fields=[
                Page.Field.id,
                Page.Field.name,
                Page.Field.category,
                Page.Field.category_list,
                Page.Field.description,
                Page.Field.about,
                Page.Field.fan_count,
                Page.Field.followers_count,
                Page.Field.checkins,
                Page.Field.link,
                Page.Field.website,
                Page.Field.phone,
                Page.Field.emails,
                Page.Field.location,
                Page.Field.hours,
                Page.Field.parking,
                Page.Field.cover,
                Page.Field.verification_status,
                Page.Field.is_verified,
                Page.Field.username,
                Page.Field.were_here_count
            ])
            
            return {
                'page_info': {
                    'id': page_info.get('id'),
                    'name': page_info.get('name'),
                    'category': page_info.get('category'),
                    'category_list': page_info.get('category_list'),
                    'description': page_info.get('description'),
                    'about': page_info.get('about'),
                    'fan_count': page_info.get('fan_count'),
                    'followers_count': page_info.get('followers_count'),
                    'checkins': page_info.get('checkins'),
                    'link': page_info.get('link'),
                    'website': page_info.get('website'),
                    'phone': page_info.get('phone'),
                    'email': page_info.get('email'),
                    'location': page_info.get('location'),
                    'hours': page_info.get('hours'),
                    'parking': page_info.get('parking'),
                    'cover': page_info.get('cover'),
                    'picture': page_info.get('picture'),
                    'verification_status': page_info.get('verification_status'),
                    'is_verified': page_info.get('is_verified'),
                    'username': page_info.get('username'),
                    'were_here_count': page_info.get('were_here_count')
                }
            }
            
        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")
    
    async def get_page_posts(self, access_token: str, page_id: str, limit: int = 25) -> Dict:
        """Lấy danh sách posts của page"""
        self._get_api(access_token)
        
        try:
            page = Page(page_id)
            posts = page.get_posts(fields=[
                PagePost.Field.id,
                PagePost.Field.message,
                PagePost.Field.story,
                PagePost.Field.created_time,
                PagePost.Field.updated_time,
                PagePost.Field.type,
                PagePost.Field.status_type,
                PagePost.Field.link,
                PagePost.Field.picture,
                PagePost.Field.full_picture,
                PagePost.Field.permalink_url,
                PagePost.Field.shares,
                PagePost.Field.reactions,
                PagePost.Field.comments
            ], params={'limit': limit})
            
            posts_list = []
            for post in posts:
                posts_list.append({
                    'id': post.get('id'),
                    'message': post.get('message'),
                    'story': post.get('story'),
                    'created_time': post.get('created_time'),
                    'updated_time': post.get('updated_time'),
                    'type': post.get('type'),
                    'status_type': post.get('status_type'),
                    'link': post.get('link'),
                    'picture': post.get('picture'),
                    'full_picture': post.get('full_picture'),
                    'permalink_url': post.get('permalink_url'),
                    'shares': post.get('shares'),
                    'reactions': post.get('reactions'),
                    'comments': post.get('comments')
                })
            
            return {
                'page_id': page_id,
                'posts': posts_list,
                'total_count': len(posts_list)
            }
            
        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_page_photos(self, access_token: str, page_id: str, limit: int = 25) -> Dict:
        """Lấy danh sách photos của page"""
        self._get_api(access_token)

        try:
            page = Page(page_id)
            photos = page.get_photos(fields=[
                Photo.Field.id,
                Photo.Field.name,
                Photo.Field.picture,
                Photo.Field.source,
                Photo.Field.height,
                Photo.Field.width,
                Photo.Field.created_time,
                Photo.Field.updated_time,
                Photo.Field.link,
                Photo.Field.album,
                Photo.Field.likes,
                Photo.Field.comments
            ], params={'limit': limit})

            photos_list = []
            for photo in photos:
                photos_list.append({
                    'id': photo.get('id'),
                    'name': photo.get('name'),
                    'picture': photo.get('picture'),
                    'source': photo.get('source'),
                    'height': photo.get('height'),
                    'width': photo.get('width'),
                    'created_time': photo.get('created_time'),
                    'updated_time': photo.get('updated_time'),
                    'link': photo.get('link'),
                    'album': photo.get('album'),
                    'likes': photo.get('likes'),
                    'comments': photo.get('comments')
                })

            return {
                'page_id': page_id,
                'photos': photos_list,
                'total_count': len(photos_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_page_albums(self, access_token: str, page_id: str, limit: int = 25) -> Dict:
        """Lấy danh sách albums của page"""
        self._get_api(access_token)

        try:
            page = Page(page_id)
            albums = page.get_albums(fields=[
                Album.Field.id,
                Album.Field.name,
                Album.Field.description,
                Album.Field.cover_photo,
                Album.Field.count,
                Album.Field.created_time,
                Album.Field.updated_time,
                Album.Field.link,
                Album.Field.type,
                Album.Field.privacy
            ], params={'limit': limit})

            albums_list = []
            for album in albums:
                albums_list.append({
                    'id': album.get('id'),
                    'name': album.get('name'),
                    'description': album.get('description'),
                    'cover_photo': album.get('cover_photo'),
                    'count': album.get('count'),
                    'created_time': album.get('created_time'),
                    'updated_time': album.get('updated_time'),
                    'link': album.get('link'),
                    'type': album.get('type'),
                    'privacy': album.get('privacy')
                })

            return {
                'page_id': page_id,
                'albums': albums_list,
                'total_count': len(albums_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_page_events(self, access_token: str, page_id: str, limit: int = 25) -> Dict:
        """Lấy danh sách events của page"""
        self._get_api(access_token)

        try:
            page = Page(page_id)
            events = page.get_events(fields=[
                Event.Field.id,
                Event.Field.name,
                Event.Field.description,
                Event.Field.start_time,
                Event.Field.end_time,
                Event.Field.place,
                Event.Field.cover,
                Event.Field.attending_count,
                Event.Field.interested_count,
                Event.Field.maybe_count,
                Event.Field.noreply_count,
                Event.Field.ticket_uri,
                Event.Field.type,
                Event.Field.category
            ], params={'limit': limit})

            events_list = []
            for event in events:
                events_list.append({
                    'id': event.get('id'),
                    'name': event.get('name'),
                    'description': event.get('description'),
                    'start_time': event.get('start_time'),
                    'end_time': event.get('end_time'),
                    'place': event.get('place'),
                    'cover': event.get('cover'),
                    'attending_count': event.get('attending_count'),
                    'interested_count': event.get('interested_count'),
                    'maybe_count': event.get('maybe_count'),
                    'noreply_count': event.get('noreply_count'),
                    'ticket_uri': event.get('ticket_uri'),
                    'type': event.get('type'),
                    'category': event.get('category')
                })

            return {
                'page_id': page_id,
                'events': events_list,
                'total_count': len(events_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_page_videos(self, access_token: str, page_id: str, limit: int = 25) -> Dict:
        """Lấy danh sách videos của page"""
        self._get_api(access_token)

        try:
            page = Page(page_id)
            videos = page.get_videos(fields=[
                AdVideo.Field.id,
                AdVideo.Field.title,
                AdVideo.Field.description,
                AdVideo.Field.created_time,
                AdVideo.Field.updated_time,
                AdVideo.Field.length,
                AdVideo.Field.picture,
                AdVideo.Field.source,
                AdVideo.Field.permalink_url
            ], params={'limit': limit})

            videos_list = []
            for video in videos:
                videos_list.append({
                    'id': video.get('id'),
                    'title': video.get('title'),
                    'description': video.get('description'),
                    'created_time': video.get('created_time'),
                    'updated_time': video.get('updated_time'),
                    'length': video.get('length'),
                    'picture': video.get('picture'),
                    'source': video.get('source'),
                    'permalink_url': video.get('permalink_url')
                })

            return {
                'page_id': page_id,
                'videos': videos_list,
                'total_count': len(videos_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def get_page_insights(self, access_token: str, page_id: str,
                              metrics: List[str] = None, period: str = "day") -> Dict:
        """Lấy insights của page"""
        self._get_api(access_token)

        try:
            page = Page(page_id)

            # Default metrics nếu không được cung cấp
            if not metrics:
                metrics = [
                    'page_impressions',
                    'page_reach',
                    'page_views_total',
                    'page_fan_adds',
                    'page_fan_removes',
                    'page_fans',
                    'page_engaged_users',
                    'page_post_engagements',
                    'page_posts_impressions',
                    'page_video_views'
                ]

            insights = page.get_insights(
                fields=['name', 'values', 'period', 'title', 'description'],
                params={
                    'metric': metrics,
                    'period': period
                }
            )

            insights_list = []
            for insight in insights:
                insights_list.append({
                    'name': insight.get('name'),
                    'values': insight.get('values'),
                    'period': insight.get('period'),
                    'title': insight.get('title'),
                    'description': insight.get('description')
                })

            return {
                'page_id': page_id,
                'period': period,
                'metrics': metrics,
                'insights': insights_list,
                'total_count': len(insights_list)
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

    async def create_page_post(self, access_token: str, page_id: str,
                             message: str, link: str = None, picture: str = None) -> Dict:
        """Tạo post mới trên page"""
        self._get_api(access_token)

        try:
            page = Page(page_id)

            params = {
                'message': message
            }

            if link:
                params['link'] = link
            if picture:
                params['picture'] = picture

            post = page.create_feed(
                fields=[],
                params=params
            )

            return {
                'page_id': page_id,
                'post_id': post.get('id'),
                'message': message,
                'link': link,
                'picture': picture
            }

        except FacebookRequestError as e:
            raise Exception(f"Facebook API error: {e}")

# Khởi tạo Facebook Page client
fb_page_client = FacebookPageClient()

# Khởi tạo MCP server
mcp = FastMCP("Facebook-Page-Server")

@mcp.tool(description="Lấy thông tin chi tiết của Facebook Page")
async def get_page_info_tool(
    ctx,
    page_id: str
) -> str:
    """
    Lấy thông tin chi tiết của Facebook Page
    
    Args:
        page_id: ID của Facebook Page
    
    Returns:
        JSON string chứa thông tin page
    """
    try:
        # Extract access token từ request headers
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"
        
        await ctx.info(f"📄 Lấy thông tin page: {page_id}")
        
        result = await fb_page_client.get_page_info(access_token, page_id)
        
        await ctx.info("✅ Lấy thông tin page thành công")
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy thông tin page: {str(e)}")
        return f"❌ Lỗi lấy thông tin page: {str(e)}"

@mcp.tool(description="Lấy danh sách posts của Facebook Page")
async def get_page_posts_tool(
    ctx,
    page_id: str,
    limit: int = 25
) -> str:
    """
    Lấy danh sách posts của Facebook Page
    
    Args:
        page_id: ID của Facebook Page
        limit: Số lượng posts tối đa (default: 25)
    
    Returns:
        JSON string chứa danh sách posts
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"
        
        await ctx.info(f"📝 Lấy posts của page: {page_id} (limit: {limit})")
        
        result = await fb_page_client.get_page_posts(access_token, page_id, limit)
        
        posts_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {posts_count} posts")
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy posts: {str(e)}")
        return f"❌ Lỗi lấy posts: {str(e)}"

@mcp.tool(description="Lấy danh sách photos của Facebook Page")
async def get_page_photos_tool(
    ctx,
    page_id: str,
    limit: int = 25
) -> str:
    """
    Lấy danh sách photos của Facebook Page
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📸 Lấy photos của page: {page_id} (limit: {limit})")

        result = await fb_page_client.get_page_photos(access_token, page_id, limit)

        photos_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {photos_count} photos")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy photos: {str(e)}")
        return f"❌ Lỗi lấy photos: {str(e)}"

@mcp.tool(description="Lấy danh sách albums của Facebook Page")
async def get_page_albums_tool(
    ctx,
    page_id: str,
    limit: int = 25
) -> str:
    """
    Lấy danh sách albums của Facebook Page
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📁 Lấy albums của page: {page_id} (limit: {limit})")

        result = await fb_page_client.get_page_albums(access_token, page_id, limit)

        albums_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {albums_count} albums")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy albums: {str(e)}")
        return f"❌ Lỗi lấy albums: {str(e)}"

@mcp.tool(description="Lấy danh sách events của Facebook Page")
async def get_page_events_tool(
    ctx,
    page_id: str,
    limit: int = 25
) -> str:
    """
    Lấy danh sách events của Facebook Page
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📅 Lấy events của page: {page_id} (limit: {limit})")

        result = await fb_page_client.get_page_events(access_token, page_id, limit)

        events_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {events_count} events")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy events: {str(e)}")
        return f"❌ Lỗi lấy events: {str(e)}"

@mcp.tool(description="Lấy danh sách videos của Facebook Page")
async def get_page_videos_tool(
    ctx,
    page_id: str,
    limit: int = 25
) -> str:
    """
    Lấy danh sách videos của Facebook Page
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"🎥 Lấy videos của page: {page_id} (limit: {limit})")

        result = await fb_page_client.get_page_videos(access_token, page_id, limit)

        videos_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {videos_count} videos")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy videos: {str(e)}")
        return f"❌ Lỗi lấy videos: {str(e)}"

@mcp.tool(description="Lấy insights của Facebook Page")
async def get_page_insights_tool(
    ctx,
    page_id: str,
    metrics: str = "",
    period: str = "day"
) -> str:
    """
    Lấy insights của Facebook Page

    Args:
        page_id: ID của Facebook Page
        metrics: Comma-separated list of metrics (optional)
        period: Time period (day, week, days_28)
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        # Parse metrics if provided
        metrics_list = None
        if metrics:
            metrics_list = [m.strip() for m in metrics.split(',')]

        await ctx.info(f"📊 Lấy insights của page: {page_id} (period: {period})")

        result = await fb_page_client.get_page_insights(access_token, page_id, metrics_list, period)

        insights_count = result.get('total_count', 0)
        await ctx.info(f"✅ Trả về {insights_count} insights")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy insights: {str(e)}")
        return f"❌ Lỗi lấy insights: {str(e)}"

@mcp.tool(description="Tạo post mới trên Facebook Page")
async def create_page_post_tool(
    ctx,
    page_id: str,
    message: str,
    link: str = "",
    picture: str = ""
) -> str:
    """
    Tạo post mới trên Facebook Page

    Args:
        page_id: ID của Facebook Page
        message: Nội dung post
        link: Link đính kèm (optional)
        picture: URL ảnh đính kèm (optional)
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Facebook access token trong Authorization header"

        await ctx.info(f"📝 Tạo post mới trên page: {page_id}")

        result = await fb_page_client.create_page_post(
            access_token, page_id, message,
            link if link else None,
            picture if picture else None
        )

        await ctx.info(f"✅ Tạo post thành công: {result.get('post_id')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo post: {str(e)}")
        return f"❌ Lỗi tạo post: {str(e)}"

# Health check endpoint
@mcp.tool(description="Health check cho Facebook Page server")
async def health_check() -> str:
    """Health check endpoint"""
    return json.dumps({
        "status": "healthy",
        "service": "Facebook Page MCP Server",
        "port": HTTP_PORT,
        "transport": "streamable-http"
    })

def main():
    """Hàm main để chạy server"""
    try:
        print("=" * 70)
        print("📄 Khởi động Facebook Page MCP Server")
        print("=" * 70)
        print("📋 Transport: Streamable HTTP (FastMCP)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print("🔧 Facebook Graph API: v20.0")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Facebook Page access token trong Authorization header")
        print()

        print("=" * 70)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
