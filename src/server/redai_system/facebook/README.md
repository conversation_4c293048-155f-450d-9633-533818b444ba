# Facebook Marketing MCP Server

MCP Server cho Facebook Marketing API sử dụng FastMCP framework với HTTP transport.

## 🎯 Tính năng

### Core Objects được hỗ trợ:
- **AdAccount** - Quản lý tài khoản quảng cáo
- **Campaign** - Quản lý chiến dịch quảng cáo
- **AdSet** - Quản lý nhóm quảng cáo
- **Ad** - Quản lý quảng cáo cụ thể
- **CustomAudience** - Quản lý đối tượng tùy chỉnh
- **AdsInsights** - <PERSON><PERSON><PERSON> cáo hiệu suất

### Tools có sẵn:
1. `get_ad_accounts_tool` - L<PERSON>y danh sách ad accounts
2. `get_campaigns_tool` - L<PERSON>y danh sách campaigns
3. `get_ad_sets_tool` - L<PERSON>y danh sách ad sets
4. `get_ads_tool` - <PERSON><PERSON><PERSON> danh sách ads
5. `get_insights_tool` - <PERSON><PERSON><PERSON> báo c<PERSON>o hiệu suất
6. `create_campaign_tool` - Tạo campaign mới
7. `get_custom_audiences_tool` - <PERSON><PERSON><PERSON> danh sách custom audiences

## 🚀 Cài đặt và Chạy

### Development:
```bash
# Install dependencies
pip install facebook-business

# Set environment variables (optional)
export FACEBOOK_APP_ID="your_app_id"
export FACEBOOK_APP_SECRET="your_app_secret"

# Run server
python src/server/redai_system/facebook/facebook_marketing_server.py
```

### Docker:
```bash
# Set environment variables
export FACEBOOK_APP_ID="your_app_id"
export FACEBOOK_APP_SECRET="your_app_secret"

# Start Facebook Marketing server
docker-compose up -d facebook-marketing

# Check logs
docker-compose logs -f facebook-marketing
```

## 🔑 Authentication

### Facebook App Setup:
1. Tạo Facebook App tại [Facebook Developers](https://developers.facebook.com/apps/)
2. Thêm **Marketing API** product
3. Lấy App ID và App Secret
4. Tạo access token với quyền `ads_management`

### Access Token:
- Sử dụng trong Authorization header: `Bearer YOUR_ACCESS_TOKEN`
- Token cần có permissions: `ads_management`, `ads_read`, `pages_read_engagement`

## 📡 API Usage

### Health Check:
```bash
curl http://localhost:8022/health
```

### MCP Tools List:
```bash
curl -X POST http://localhost:8022/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "id": 1, "method": "tools/list"}'
```

### Get Ad Accounts:
```bash
curl -X POST http://localhost:8022/mcp \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "get_ad_accounts_tool",
      "arguments": {}
    }
  }'
```

### Get Campaigns:
```bash
curl -X POST http://localhost:8022/mcp \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "get_campaigns_tool",
      "arguments": {
        "account_id": "act_*********"
      }
    }
  }'
```

### Create Campaign:
```bash
curl -X POST http://localhost:8022/mcp \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "create_campaign_tool",
      "arguments": {
        "account_id": "act_*********",
        "name": "My New Campaign",
        "objective": "TRAFFIC"
      }
    }
  }'
```

### Get Insights:
```bash
curl -X POST http://localhost:8022/mcp \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "get_insights_tool",
      "arguments": {
        "account_id": "act_*********",
        "level": "campaign",
        "date_preset": "last_7d"
      }
    }
  }'
```

## 🔧 Configuration

### Environment Variables:
- `FACEBOOK_APP_ID` - Facebook App ID
- `FACEBOOK_APP_SECRET` - Facebook App Secret
- `FACEBOOK_MARKETING_HTTP_HOST` - Server host (default: 127.0.0.1)
- `FACEBOOK_MARKETING_HTTP_PORT` - Server port (default: 8022)
- `FACEBOOK_MARKETING_HTTP_PATH` - MCP endpoint path (default: /mcp)

### MCP Client Configuration:
```json
{
  "mcpServers": {
    "facebook-marketing": {
      "url": "http://localhost:8022/mcp"
    }
  }
}
```

## 📊 Response Examples

### Ad Accounts Response:
```json
{
  "ad_accounts": [
    {
      "id": "act_*********",
      "name": "My Ad Account",
      "account_status": "1",
      "currency": "USD",
      "timezone_name": "America/Los_Angeles",
      "amount_spent": "1000",
      "balance": "5000"
    }
  ],
  "total_count": 1
}
```

### Campaigns Response:
```json
{
  "account_id": "act_*********",
  "campaigns": [
    {
      "id": "*********",
      "name": "My Campaign",
      "status": "ACTIVE",
      "objective": "TRAFFIC",
      "created_time": "2024-01-01T00:00:00+0000",
      "updated_time": "2024-01-02T00:00:00+0000"
    }
  ],
  "total_count": 1
}
```

## 🐳 Docker Support

Server được containerized và có thể chạy với Docker Compose cùng với các servers khác trong hệ thống.

Port: **8022**
Container: **facebook-marketing-server**

## 📝 Notes

- Server sử dụng Facebook Business SDK v23.0.0+
- Hỗ trợ tất cả core objects của Facebook Marketing API
- Authentication qua Bearer token trong headers
- Tuân thủ MCP protocol với FastMCP framework
- Error handling và logging đầy đủ
