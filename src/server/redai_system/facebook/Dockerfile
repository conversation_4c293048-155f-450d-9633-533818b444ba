# Facebook MCP Servers Dockerfile (Marketing + Page)
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p /app/logs

# Expose ports for both Facebook servers
EXPOSE 8022 8023

# Create startup script
RUN echo '#!/bin/bash\n\
SERVER_TYPE=${SERVER_TYPE:-marketing}\n\
\n\
if [ "$SERVER_TYPE" = "marketing" ]; then\n\
    echo "🚀 Starting Facebook Marketing MCP Server..."\n\
    exec python src/server/redai_system/facebook/facebook_marketing_server.py\n\
elif [ "$SERVER_TYPE" = "page" ]; then\n\
    echo "🚀 Starting Facebook Page MCP Server..."\n\
    exec python src/server/redai_system/facebook/facebook_page_server.py\n\
else\n\
    echo "❌ Invalid SERVER_TYPE: $SERVER_TYPE"\n\
    echo "💡 Use SERVER_TYPE=marketing or SERVER_TYPE=page"\n\
    exit 1\n\
fi' > /app/start-server.sh && \
    chmod +x /app/start-server.sh

# Health check script (dynamic port detection)
RUN echo '#!/bin/bash\n\
SERVER_TYPE=${SERVER_TYPE:-marketing}\n\
if [ "$SERVER_TYPE" = "marketing" ]; then\n\
    port=8022\n\
elif [ "$SERVER_TYPE" = "page" ]; then\n\
    port=8023\n\
else\n\
    echo "❌ Invalid SERVER_TYPE for health check"\n\
    exit 1\n\
fi\n\
curl -f http://localhost:$port/health || exit 1' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# Default command
CMD ["/app/start-server.sh"]
