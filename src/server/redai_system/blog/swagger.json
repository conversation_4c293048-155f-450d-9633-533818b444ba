{"openapi": "3.0.0", "info": {"title": "Blog Module API", "description": "API documentation for Blog Module - Q<PERSON><PERSON>n lý hệ thống blog cho người dùng bao gồm tạo bài viết, quản lý bình luận và mua bài viết", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Blogs", "description": "<PERSON><PERSON><PERSON><PERSON> lý bài viết cho người dùng"}, {"name": "User - Blog Comments", "description": "<PERSON><PERSON><PERSON><PERSON> lý bình luận bài viết cho người dùng"}, {"name": "User - Blog Purchases", "description": "<PERSON><PERSON><PERSON><PERSON> lý mua bài viết cho người dùng"}], "paths": {"/user/blogs": {"get": {"tags": ["User - Blogs"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch b<PERSON>i vi<PERSON>t", "description": "<PERSON><PERSON><PERSON> danh sách bài viết với các tùy chọn lọc nâng cao (theo tr<PERSON><PERSON> thái, lo<PERSON>i tác gi<PERSON>, t<PERSON><PERSON> ki<PERSON>, loại sở hữu). <PERSON><PERSON> thể lọc theo blog người dùng tạo (CREATED), blog người dùng đã mua (PURCHASED), hoặc blog chưa sở hữu (NOT_OWNED). Khi lọc theo ownership_type=CREATED, sẽ trả về tất cả blog của người dùng với mọi trạng thái. Khi lọc theo ownership_type=PURCHASED hoặc NOT_OWNED, chỉ lọc theo trạng thái APPROVED. Lưu ý: Trường content sẽ trả về null nếu người dùng chưa mua bài viết hoặc không phải tác giả của bài viết.", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái bài viết", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "PENDING", "APPROVED", "REJECTED"], "example": "APPROVED"}}, {"name": "authorType", "in": "query", "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i tác gi<PERSON>", "required": false, "schema": {"type": "string", "enum": ["USER", "SYSTEM"], "example": "USER"}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tiêu đề hoặc mô tả", "required": false, "schema": {"type": "string", "example": "javascript tutorial"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["createdAt", "updatedAt", "viewCount", "like", "point"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}, {"name": "ownership_type", "in": "query", "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i sở hữu", "required": false, "schema": {"type": "string", "enum": ["CREATED", "PURCHASED", "NOT_OWNED"], "example": "CREATED"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách bài viết thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedBlogResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Blogs"], "summary": "<PERSON><PERSON><PERSON> bài viết mới", "description": "Tạo bài viết mới và trả về URL để upload nội dung và thumbnail", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> bài viết thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Blog created successfully"}, "result": {"$ref": "#/components/schemas/CreateBlogResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/detail/{id}": {"get": {"tags": ["User - Blogs"], "summary": "<PERSON><PERSON><PERSON> chi tiết bài viết", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một bài viết theo ID. Lưu ý: Trường content sẽ trả về null nếu người dùng chưa mua bài viết hoặc không phải tác giả của bài viết.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết bài viết thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/BlogResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/{id}/media": {"put": {"tags": ["User - Blogs"], "summary": "<PERSON>ập nhật media bài viết", "description": "<PERSON><PERSON><PERSON> nhật nội dung hoặc thumbnail của bài viết và trả về URL để upload", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogMediaDto"}}}}, "responses": {"200": {"description": "<PERSON>ập nhật media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Media updated successfully"}, "result": {"$ref": "#/components/schemas/UpdateMediaResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/{id}/submit": {"put": {"tags": ["User - Blogs"], "summary": "<PERSON><PERSON><PERSON> bài viết để kiểm du<PERSON>t", "description": "<PERSON><PERSON><PERSON> bài viết từ trạng thái DRAFT sang PENDING để chờ kiểm duyệt", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> bài viết để kiểm duyệt thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Blog submitted for review successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/{id}": {"delete": {"tags": ["User - Blogs"], "summary": "<PERSON>óa b<PERSON>i vi<PERSON>t", "description": "<PERSON><PERSON><PERSON> bài viết (chỉ có thể xóa bài viết ở trạng thái DRAFT hoặc REJECTED)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> bài viết thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Blog deleted successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/{blogId}/comments": {"post": {"tags": ["User - Blog Comments"], "summary": "<PERSON><PERSON><PERSON> b<PERSON>nh luận mới", "description": "<PERSON><PERSON><PERSON> b<PERSON>nh luận mới cho bài viết", "security": [{"bearerAuth": []}], "parameters": [{"name": "blogId", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogCommentDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> bình luận thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Comment created successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "get": {"tags": ["User - Blog Comments"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch b<PERSON>nh lu<PERSON>n", "description": "<PERSON><PERSON><PERSON> danh sách bình luận của bài viết với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "blogId", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách bình luận thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedBlogCommentsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/comments/{id}": {"delete": {"tags": ["User - Blog Comments"], "summary": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "description": "<PERSON><PERSON>a bình luận theo ID (chỉ có thể xóa bình luận của ch<PERSON>h mình)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a b<PERSON>nh luận", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> bình luận thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Comment deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/{blogId}/purchase": {"post": {"tags": ["User - Blog Purchases"], "summary": "<PERSON><PERSON> b<PERSON>i vi<PERSON>t", "description": "Mua bài viết bằng point", "security": [{"bearerAuth": []}], "parameters": [{"name": "blogId", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"201": {"description": "<PERSON><PERSON> bài viết thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Blog purchased successfully"}, "result": {"type": "null"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/{blogId}/purchased": {"get": {"tags": ["User - Blog Purchases"], "summary": "<PERSON><PERSON><PERSON> tra trạng thái mua", "description": "<PERSON><PERSON><PERSON> tra người dùng đã mua bài viết chưa", "security": [{"bearerAuth": []}], "parameters": [{"name": "blogId", "in": "path", "description": "ID của bài viết", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> tra trạng thái mua thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/BlogPurchaseStatusDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/purchases": {"get": {"tags": ["User - Blog Purchases"], "summary": "<PERSON><PERSON><PERSON> danh sách bài viết đã mua", "description": "<PERSON><PERSON><PERSON> danh sách bài viết đã mua với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách bài viết đã mua thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedPurchasedBlogsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/blogs/purchases/{purchaseId}": {"get": {"tags": ["User - Blog Purchases"], "summary": "<PERSON><PERSON><PERSON> chi tiết giao dịch mua bài viết", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết về giao dịch mua bài viết", "security": [{"bearerAuth": []}], "parameters": [{"name": "purchaseId", "in": "path", "description": "ID của giao d<PERSON>ch mua", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết giao dịch thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/BlogPurchaseDetailDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"PaginatedBlogResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/BlogResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số bài viết", "example": 100}, "itemCount": {"type": "integer", "description": "Số bài viết trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "<PERSON><PERSON> bài viết mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "BlogResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của bài viết", "example": 1}, "title": {"type": "string", "description": "Ti<PERSON><PERSON> đề bài viết", "example": "Hướng dẫn lập trình <PERSON>"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả bài viết", "example": "<PERSON><PERSON><PERSON> viết hướng dẫn c<PERSON> bản về JavaScript"}, "content": {"type": "string", "description": "URL file content trên CDN (null nếu chưa mua hoặc không phải tác giả)", "example": "https://cdn.example.com/blogs/content/123.html", "nullable": true}, "point": {"type": "integer", "description": "Số point cần để mua bài viết", "example": 100}, "viewCount": {"type": "integer", "description": "S<PERSON> l<PERSON> xem", "example": 150}, "thumbnailUrl": {"type": "string", "description": "URL thumbnail", "example": "https://cdn.example.com/blogs/thumbnails/123.jpg", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Tags của bài viết", "example": ["javascript", "tutorial", "programming"]}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": 1632474086123}, "author": {"$ref": "#/components/schemas/AuthorDto"}, "employeeModerator": {"type": "integer", "description": "ID nhân viên ki<PERSON>", "example": null, "nullable": true}, "status": {"type": "string", "enum": ["DRAFT", "PENDING", "APPROVED", "REJECTED"], "description": "<PERSON>r<PERSON><PERSON> thái bài viết", "example": "APPROVED"}, "enable": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> thái hiển thị", "example": true}, "like": {"type": "integer", "description": "<PERSON><PERSON> l<PERSON> th<PERSON>ch", "example": 45}, "isPurchased": {"type": "boolean", "description": "Trạng thái đã mua của người dùng hiện tại", "example": true, "nullable": true}}, "required": ["id", "title", "point", "viewCount", "tags", "createdAt", "updatedAt", "author", "status", "enable", "like"]}, "AuthorDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tác giả", "example": 1, "nullable": true}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"type": "string", "enum": ["USER", "SYSTEM"], "description": "Loại tác giả", "example": "USER"}, "avatar": {"type": "string", "description": "Avatar của tác giả", "example": "https://cdn.example.com/avatars/user10.jpg", "nullable": true}}, "required": ["name", "type"]}, "CreateBlogDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Ti<PERSON><PERSON> đề bài viết", "example": "Hướng dẫn lập trình <PERSON>", "maxLength": 500}, "description": {"type": "string", "description": "<PERSON><PERSON> tả bài viết", "example": "<PERSON><PERSON><PERSON> viết hướng dẫn c<PERSON> bản về JavaScript", "maxLength": 1000}, "contentMediaType": {"type": "string", "description": "Loại media của nội dung", "example": "text/html"}, "thumbnailMediaType": {"type": "string", "description": "Loại media của thumbnail", "example": "image/jpeg"}, "point": {"type": "integer", "description": "Số point để mua bài viết", "example": 100, "minimum": 0}, "tags": {"type": "array", "items": {"type": "string", "maxLength": 50}, "description": "Tags của bài viết", "example": ["javascript", "tutorial"]}, "status": {"type": "string", "enum": ["DRAFT", "PENDING", "APPROVED", "REJECTED"], "description": "<PERSON>r<PERSON><PERSON> thái bài viết", "example": "DRAFT", "default": "DRAFT"}}, "required": ["title", "description", "contentMediaType", "thumbnailMediaType", "point", "tags"]}, "CreateBlogResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của bài viết vừa tạo", "example": 1}, "contentUploadUrl": {"type": "string", "description": "URL để upload nội dung bài viết", "example": "https://s3.amazonaws.com/bucket/upload-content-url"}, "thumbnailUploadUrl": {"type": "string", "description": "URL để upload thumbnail", "example": "https://s3.amazonaws.com/bucket/upload-thumbnail-url"}}, "required": ["id", "contentUploadUrl", "thumbnailUploadUrl"]}, "UpdateBlogMediaDto": {"type": "object", "properties": {"media_type": {"type": "string", "enum": ["content", "thumbnail"], "description": "Loại media cần cập nh<PERSON>t", "example": "content"}, "media_content_type": {"type": "string", "description": "Loại nội dung media", "example": "text/html"}}, "required": ["media_type", "media_content_type"]}, "UpdateMediaResponseDto": {"type": "object", "properties": {"uploadUrl": {"type": "string", "description": "URL để upload media", "example": "https://s3.amazonaws.com/bucket/upload-url"}}, "required": ["uploadUrl"]}, "CreateBlogCommentDto": {"type": "object", "properties": {"content": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung bình luận", "example": "Bài viết rất hay và bổ ích!", "maxLength": 1000}, "parentCommentId": {"type": "integer", "description": "<PERSON> bình luận cha (để tạo reply)", "example": 1, "nullable": true}}, "required": ["content"]}, "BlogCommentDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> b<PERSON>nh luận", "example": 1}, "blogId": {"type": "integer", "description": "ID bài viết", "example": 1}, "userId": {"type": "integer", "description": "ID người dùng", "example": 10, "nullable": true}, "employeeId": {"type": "integer", "description": "ID nhân viên", "example": null, "nullable": true}, "content": {"type": "string", "description": "<PERSON><PERSON><PERSON> dung bình luận", "example": "Bài viết rất hay và bổ ích!"}, "authorType": {"type": "string", "enum": ["USER", "SYSTEM"], "description": "<PERSON><PERSON><PERSON> tác g<PERSON><PERSON> bình luận", "example": "USER"}, "parentCommentId": {"type": "integer", "description": "<PERSON> bình luận cha", "example": null, "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}, "replies": {"type": "array", "items": {"$ref": "#/components/schemas/BlogCommentDto"}, "description": "<PERSON><PERSON> s<PERSON>ch bình luận con"}}, "required": ["id", "blogId", "content", "authorType", "createdAt", "replies"]}, "PaginatedBlogCommentsResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/BlogCommentDto"}}, "totalItems": {"type": "integer", "description": "<PERSON><PERSON><PERSON> số bình luận", "example": 50}, "itemCount": {"type": "integer", "description": "<PERSON><PERSON> bình luận trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "<PERSON><PERSON> bình luận mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "BlogPurchaseStatusDto": {"type": "object", "properties": {"purchased": {"type": "boolean", "description": "Tr<PERSON>ng thái đã mua", "example": true}, "purchased_at": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian mua (Unix timestamp)", "example": 1632474086123, "nullable": true}}, "required": ["purchased"]}, "PurchasedBlogDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID giao dịch mua", "example": 1}, "blogId": {"type": "integer", "description": "ID bài viết", "example": 1}, "userId": {"type": "integer", "description": "ID người mua", "example": 10}, "point": {"type": "integer", "description": "Số point đã trả", "example": 100}, "purchased_at": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian mua (Unix timestamp)", "example": 1632474086123}, "blog": {"$ref": "#/components/schemas/BlogResponseDto"}}, "required": ["id", "blogId", "userId", "point", "purchased_at", "blog"]}, "PaginatedPurchasedBlogsResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PurchasedBlogDto"}}, "totalItems": {"type": "integer", "description": "Tổng số bài viết đã mua", "example": 25}, "itemCount": {"type": "integer", "description": "Số bài viết trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "<PERSON><PERSON> bài viết mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "BlogPurchaseDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID giao dịch mua", "example": 1}, "point": {"type": "integer", "description": "Số point đã trả", "example": 100}, "purchased_at": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian mua (Unix timestamp)", "example": 1632474086123}, "blog": {"$ref": "#/components/schemas/BlogResponseDto"}, "buyer": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID người mua", "example": 10}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> mua", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "description": "Avatar ngư<PERSON>i mua", "example": "https://cdn.example.com/avatars/user20.jpg", "nullable": true}}, "required": ["id", "name"]}}, "required": ["id", "point", "purchased_at", "blog", "buyer"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 500}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}