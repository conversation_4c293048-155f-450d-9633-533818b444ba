# RedAI Blog MCP Server

Server MCP cho RedAI Blog Module API sử dụng FastMCP và OpenAPI Schema. Server này tự động tạo các MCP tools và resources từ `swagger.json`, cung cấp giao diện MCP cho tất cả các endpoint của Blog Module API.

## Tính năng

- ✅ **Tự động tạo tools từ OpenAPI specification**
- 🔑 **Hỗ trợ Bearer token authentication từ client**
- 🚀 **Streamable HTTP transport với FastMCP**
- 🔧 **Tùy chỉnh route mapping cho các loại endpoint khác nhau**
- 📊 **Xử lý parameters, headers và request body tự động**
- 📝 **Tools tùy chỉnh cho blog functionality**

## Cấu trúc API

### Blogs (Quản lý bài viết)
- `GET /user/blogs` - Lấy danh sách bài viết với filter nâng cao
- `POST /user/blogs` - <PERSON><PERSON><PERSON> bài viết mới
- `GET /user/blogs/detail/{id}` - <PERSON><PERSON><PERSON> chi tiết bài viết
- `PUT /user/blogs/{id}/media` - Cập nhật media (content/thumbnail)
- `PUT /user/blogs/{id}/submit` - Gửi bài viết để kiểm duyệt
- `DELETE /user/blogs/{id}` - Xóa bài viết

### Comments (Quản lý bình luận)
- `GET /user/blogs/{blogId}/comments` - Lấy danh sách bình luận
- `POST /user/blogs/{blogId}/comments` - Tạo bình luận mới
- `DELETE /user/blogs/comments/{id}` - Xóa bình luận

### Purchases (Quản lý mua bài viết)
- `POST /user/blogs/{blogId}/purchase` - Mua bài viết bằng point
- `GET /user/blogs/{blogId}/purchased` - Kiểm tra trạng thái mua
- `GET /user/blogs/purchases` - Lấy danh sách bài viết đã mua
- `GET /user/blogs/purchases/{purchaseId}` - Chi tiết giao dịch mua

## Cấu hình

### Biến môi trường

```bash
# API Configuration
REDAI_BLOG_API_BASE_URL=https://api.redai.com

# Server Configuration
BLOG_HTTP_HOST=127.0.0.1
BLOG_HTTP_PORT=8005
BLOG_HTTP_PATH=/mcp
BLOG_TRANSPORT=streamable-http
```

### Authentication

Server sử dụng Bearer token authentication:
- Bearer token được truyền từ client khi kết nối
- Tự động xử lý authentication cho tất cả requests
- Sử dụng tool `update_bearer_token` để cấu hình authentication

## Cài đặt và chạy

### 1. Cài đặt dependencies

```bash
pip install fastmcp>=2.3.0 httpx
# hoặc
pip install -e .
```

### 2. Chạy server

```bash
# Chạy với Streamable HTTP transport (mặc định)
python src/server/redai_system/blog/blog_server.py

# Chạy với transport cụ thể
python src/server/redai_system/blog/blog_server.py streamable-http
python src/server/redai_system/blog/blog_server.py sse
python src/server/redai_system/blog/blog_server.py stdio
```

Server sẽ khởi động tại:
- **MCP Endpoint**: `http://127.0.0.1:8005/mcp`
- **Server URL**: `http://127.0.0.1:8005`

## Sử dụng

### Kết nối từ MCP Client

```python
from fastmcp import Client

async def main():
    # Kết nối qua HTTP
    async with Client("http://127.0.0.1:8005/mcp") as client:
        # Cập nhật authentication
        await client.call_tool("update_bearer_token", {
            "bearer_token": "your_bearer_token_here"
        })
        
        # Lấy danh sách bài viết
        blogs = await client.call_tool("get_user_blogs", {
            "page": 1,
            "limit": 10,
            "status": "APPROVED",
            "ownership_type": "CREATED"
        })
        
        # Tạo bài viết mới
        new_blog = await client.call_tool("post_user_blogs", {
            "title": "Hướng dẫn lập trình JavaScript",
            "description": "Bài viết hướng dẫn cơ bản về JavaScript",
            "contentMediaType": "text/html",
            "thumbnailMediaType": "image/jpeg",
            "point": 100,
            "tags": ["javascript", "tutorial", "programming"]
        })
        
        # Mua bài viết
        purchase = await client.call_tool("post_user_blogs_blogId_purchase", {
            "blogId": 123
        })
```

### Tools có sẵn

#### Authentication Tools
- `update_bearer_token` - Cập nhật Bearer token cho authentication
- `check_auth_status` - Kiểm tra trạng thái authentication hiện tại

#### Blog Tools
- `get_blog_summary` - Lấy tổng quan về blog của người dùng
- `calculate_blog_stats` - Tính toán engagement stats (like rate, comment rate)
- `suggest_blog_tags` - Gợi ý tags cho bài viết dựa trên tiêu đề và mô tả

#### API Tools (tự động tạo từ OpenAPI)

**Blog Management:**
- `get_user_blogs` - Lấy danh sách bài viết
- `post_user_blogs` - Tạo bài viết mới
- `get_user_blogs_detail_id` - Lấy chi tiết bài viết
- `put_user_blogs_id_media` - Cập nhật media
- `put_user_blogs_id_submit` - Gửi kiểm duyệt
- `delete_user_blogs_id` - Xóa bài viết

**Comment Management:**
- `get_user_blogs_blogId_comments` - Lấy danh sách bình luận
- `post_user_blogs_blogId_comments` - Tạo bình luận
- `delete_user_blogs_comments_id` - Xóa bình luận

**Purchase Management:**
- `post_user_blogs_blogId_purchase` - Mua bài viết
- `get_user_blogs_blogId_purchased` - Kiểm tra trạng thái mua
- `get_user_blogs_purchases` - Lấy danh sách đã mua
- `get_user_blogs_purchases_purchaseId` - Chi tiết giao dịch

### Ví dụ sử dụng tools

```python
# Tính toán engagement stats
stats = await client.call_tool("calculate_blog_stats", {
    "total_views": 1000,
    "total_likes": 150,
    "total_comments": 25
})

# Gợi ý tags cho bài viết
tags = await client.call_tool("suggest_blog_tags", {
    "title": "Hướng dẫn lập trình JavaScript cho người mới bắt đầu",
    "description": "Bài viết này sẽ hướng dẫn các khái niệm cơ bản về JavaScript"
})

# Lấy tổng quan blog
summary = await client.call_tool("get_blog_summary", {})
```

## Workflow sử dụng

### 1. Tạo và quản lý bài viết

```python
# 1. Tạo bài viết mới (trạng thái DRAFT)
blog = await client.call_tool("post_user_blogs", {
    "title": "Tiêu đề bài viết",
    "description": "Mô tả bài viết",
    "contentMediaType": "text/html",
    "thumbnailMediaType": "image/jpeg",
    "point": 100,
    "tags": ["tag1", "tag2"]
})

# 2. Upload content và thumbnail sử dụng URLs từ response
# (Sử dụng contentUploadUrl và thumbnailUploadUrl)

# 3. Gửi bài viết để kiểm duyệt
await client.call_tool("put_user_blogs_id_submit", {
    "id": blog_id
})

# 4. Kiểm tra trạng thái bài viết
detail = await client.call_tool("get_user_blogs_detail_id", {
    "id": blog_id
})
```

### 2. Quản lý bình luận

```python
# Lấy bình luận của bài viết
comments = await client.call_tool("get_user_blogs_blogId_comments", {
    "blogId": 123,
    "page": 1,
    "limit": 10
})

# Tạo bình luận mới
await client.call_tool("post_user_blogs_blogId_comments", {
    "blogId": 123,
    "content": "Bài viết rất hay!",
    "parentCommentId": null  # hoặc ID của comment cha để reply
})
```

### 3. Mua bài viết

```python
# Kiểm tra trạng thái mua
status = await client.call_tool("get_user_blogs_blogId_purchased", {
    "blogId": 123
})

# Mua bài viết nếu chưa mua
if not status["purchased"]:
    await client.call_tool("post_user_blogs_blogId_purchase", {
        "blogId": 123
    })

# Xem danh sách bài viết đã mua
purchases = await client.call_tool("get_user_blogs_purchases", {
    "page": 1,
    "limit": 10
})
```

## Cấu hình MCP Client

### Claude Desktop

```json
{
  "mcpServers": {
    "redai-blog": {
      "command": "python",
      "args": ["src/server/redai_system/blog/blog_server.py"],
      "env": {
        "REDAI_BLOG_API_BASE_URL": "https://api.redai.com",
        "BLOG_HTTP_HOST": "127.0.0.1",
        "BLOG_HTTP_PORT": "8005"
      }
    }
  }
}
```

### Codeium/Continue

```json
{
  "mcpServers": {
    "redai-blog": {
      "url": "http://127.0.0.1:8005/mcp"
    }
  }
}
```

## Troubleshooting

### Lỗi thường gặp

**Lỗi**: `FileNotFoundError: Không tìm thấy file schema tại: swagger.json`
- **Giải pháp**: Đảm bảo file `swagger.json` tồn tại trong thư mục `blog/`

**Lỗi**: `ImportError: No module named 'fastmcp'`
- **Giải pháp**: Cài đặt dependencies: `pip install fastmcp>=2.3.0`

**Lỗi**: `ConnectionError` khi gọi API
- **Giải pháp**: Kiểm tra Bearer token và API base URL

**Lỗi**: `403 Forbidden` khi xóa bài viết
- **Giải pháp**: Chỉ có thể xóa bài viết ở trạng thái DRAFT hoặc REJECTED

## Tài liệu tham khảo

- [FastMCP Documentation](https://github.com/jlowin/fastmcp)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [OpenAPI Specification](https://swagger.io/specification/)
