# Dockerfile for Google MCP Servers
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project
COPY . .

# Set Python path
ENV PYTHONPATH=/app

# Create logs directory
RUN mkdir -p /app/logs

# Expose ports for all Google servers
EXPOSE 8015 8016 8017 8018 8019 8020 8021

# Health check script
COPY src/server/redai_system/google/healthcheck.sh /app/healthcheck.sh
RUN chmod +x /app/healthcheck.sh

# Default command (will be overridden in docker-compose)
CMD ["python", "src/server/redai_system/google/google_sheets_server.py"]
