#!/usr/bin/env python3
"""
Test script để kiểm tra c<PERSON><PERSON> hình Google Ads MCP Server

Kiểm tra:
1. Environment variables
2. Transport configuration
3. Authentication setup
4. Server connectivity

Author: RedAI Team
Date: 2025-01-16
"""

import os
import sys
import asyncio
import httpx
from typing import Dict, Any

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
sys.path.insert(0, project_root)

def check_environment_variables() -> Dict[str, Any]:
    """Kiểm tra các biến môi trường cần thiết"""
    print("🔍 Kiểm tra Environment Variables...")
    
    required_vars = [
        "GOOGLE_ADS_DEVELOPER_TOKEN",
        "GOOGLE_ADS_LOGIN_CUSTOMER_ID"
    ]
    
    optional_vars = [
        "GOOGLE_OAUTH2_CLIENT_ID",
        "GOOGLE_OAUTH2_CLIENT_SECRET",
        "GOOGLE_OAUTH2_REDIRECT_URI",
        "GOOGLE_ADS_TRANSPORT",
        "GOOGLE_ADS_HTTP_HOST",
        "GOOGLE_ADS_HTTP_PORT",
        "GOOGLE_ADS_HTTP_PATH",
        "GOOGLE_ADS_SSE_HOST",
        "GOOGLE_ADS_SSE_PORT",
        "GOOGLE_ADS_SSE_PATH"
    ]
    
    results = {
        "required": {},
        "optional": {},
        "missing_required": [],
        "status": "success"
    }
    
    # Kiểm tra required variables
    for var in required_vars:
        value = os.getenv(var)
        if value:
            results["required"][var] = "✅ Set" if value != "your_developer_token_here" else "⚠️  Default value"
        else:
            results["required"][var] = "❌ Missing"
            results["missing_required"].append(var)
    
    # Kiểm tra optional variables
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            results["optional"][var] = f"✅ {value}"
        else:
            results["optional"][var] = "❌ Not set"
    
    if results["missing_required"]:
        results["status"] = "error"
    
    return results

def check_transport_config() -> Dict[str, Any]:
    """Kiểm tra cấu hình transport"""
    print("🚀 Kiểm tra Transport Configuration...")
    
    transport = os.getenv("GOOGLE_ADS_TRANSPORT", "http").lower()
    http_host = os.getenv("GOOGLE_ADS_HTTP_HOST", "127.0.0.1")
    http_port = int(os.getenv("GOOGLE_ADS_HTTP_PORT", "8020"))
    http_path = os.getenv("GOOGLE_ADS_HTTP_PATH", "/mcp")
    
    sse_host = os.getenv("GOOGLE_ADS_SSE_HOST", "127.0.0.1")
    sse_port = int(os.getenv("GOOGLE_ADS_SSE_PORT", "8120"))
    sse_path = os.getenv("GOOGLE_ADS_SSE_PATH", "/sse")
    
    return {
        "transport_type": transport,
        "http_config": {
            "host": http_host,
            "port": http_port,
            "path": http_path,
            "url": f"http://{http_host}:{http_port}{http_path}"
        },
        "sse_config": {
            "host": sse_host,
            "port": sse_port,
            "path": sse_path,
            "url": f"http://{sse_host}:{sse_port}{sse_path}"
        }
    }

async def check_server_connectivity(config: Dict[str, Any]) -> Dict[str, Any]:
    """Kiểm tra kết nối đến server"""
    print("🌐 Kiểm tra Server Connectivity...")
    
    results = {
        "http_health": "❌ Failed",
        "sse_health": "❌ Failed",
        "mcp_endpoint": "❌ Failed"
    }
    
    async with httpx.AsyncClient(timeout=5.0) as client:
        # Test HTTP health endpoint
        try:
            http_health_url = f"http://{config['http_config']['host']}:{config['http_config']['port']}/health"
            response = await client.get(http_health_url)
            if response.status_code == 200:
                results["http_health"] = "✅ OK"
            else:
                results["http_health"] = f"⚠️  Status {response.status_code}"
        except Exception as e:
            results["http_health"] = f"❌ {str(e)}"
        
        # Test MCP endpoint
        try:
            mcp_url = config['http_config']['url']
            response = await client.post(mcp_url, json={"method": "ping"})
            if response.status_code in [200, 404, 405]:  # 404/405 expected without proper MCP request
                results["mcp_endpoint"] = "✅ Reachable"
            else:
                results["mcp_endpoint"] = f"⚠️  Status {response.status_code}"
        except Exception as e:
            results["mcp_endpoint"] = f"❌ {str(e)}"
        
        # Test SSE endpoint (if different from HTTP)
        if config['sse_config']['port'] != config['http_config']['port']:
            try:
                sse_health_url = f"http://{config['sse_config']['host']}:{config['sse_config']['port']}/health"
                response = await client.get(sse_health_url)
                if response.status_code == 200:
                    results["sse_health"] = "✅ OK"
                else:
                    results["sse_health"] = f"⚠️  Status {response.status_code}"
            except Exception as e:
                results["sse_health"] = f"❌ {str(e)}"
        else:
            results["sse_health"] = "✅ Same as HTTP"
    
    return results

def print_results(env_results: Dict, transport_config: Dict, connectivity_results: Dict):
    """In kết quả kiểm tra"""
    print("\n" + "="*60)
    print("📊 KẾT QUẢ KIỂM TRA GOOGLE ADS MCP SERVER")
    print("="*60)
    
    # Environment Variables
    print("\n🔧 ENVIRONMENT VARIABLES:")
    print("Required Variables:")
    for var, status in env_results["required"].items():
        print(f"  {var}: {status}")
    
    if env_results["missing_required"]:
        print(f"\n❌ Missing required variables: {', '.join(env_results['missing_required'])}")
    
    print("\nOptional Variables:")
    for var, status in env_results["optional"].items():
        print(f"  {var}: {status}")
    
    # Transport Configuration
    print(f"\n🚀 TRANSPORT CONFIGURATION:")
    print(f"  Transport Type: {transport_config['transport_type']}")
    print(f"  HTTP URL: {transport_config['http_config']['url']}")
    print(f"  SSE URL: {transport_config['sse_config']['url']}")
    
    # Connectivity
    print(f"\n🌐 SERVER CONNECTIVITY:")
    print(f"  HTTP Health: {connectivity_results['http_health']}")
    print(f"  MCP Endpoint: {connectivity_results['mcp_endpoint']}")
    print(f"  SSE Health: {connectivity_results['sse_health']}")
    
    # Overall Status
    print(f"\n📋 OVERALL STATUS:")
    if env_results["status"] == "error":
        print("❌ FAILED - Missing required environment variables")
        return False
    elif "✅" in str(connectivity_results.values()):
        print("✅ READY - Server configuration looks good")
        return True
    else:
        print("⚠️  WARNING - Server may not be running")
        return True

async def main():
    """Main test function"""
    print("🧪 Google Ads MCP Server Configuration Test")
    print("=" * 50)
    
    # Load environment variables from .env if exists
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Loaded .env file")
    except ImportError:
        print("⚠️  python-dotenv not installed, skipping .env file")
    except Exception as e:
        print(f"⚠️  Could not load .env file: {e}")
    
    # Run checks
    env_results = check_environment_variables()
    transport_config = check_transport_config()
    connectivity_results = await check_server_connectivity(transport_config)
    
    # Print results
    success = print_results(env_results, transport_config, connectivity_results)
    
    if not success:
        print("\n💡 NEXT STEPS:")
        print("1. Set required environment variables in .env file")
        print("2. Get Google Ads API credentials from: https://ads.google.com/nav/selectaccount?authuser=0&dst=/aw/apicenter")
        print("3. Start the server: python src/server/redai_system/google/google_ads_server.py")
        sys.exit(1)
    else:
        print("\n🎉 Configuration test completed!")

if __name__ == "__main__":
    asyncio.run(main())
