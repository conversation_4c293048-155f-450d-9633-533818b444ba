#!/usr/bin/env python3
"""
Google Sheets MCP Server sử dụng FastMCP và Google Sheets API v4

Server này cung cấp các MCP tools để tương tác với Google Sheets API,
bao gồm đọc, ghi, tạo và quản lý spreadsheets.

Tính năng:
- Tạo và quản lý spreadsheets
- Đ<PERSON><PERSON> và ghi dữ liệu cells
- Batch operations cho hiệu suất cao
- Quản lý sheets trong spreadsheet
- Formatting và conditional formatting
- Authentication với Google OAuth2

Transport: Streamable HTTP
Authentication: Google OAuth2 với service account hoặc user credentials
"""

import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Union
from fastmcp import FastMCP
from fastmcp.server.dependencies import get_http_request
from google.oauth2 import credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import httpx

# Cấu hình server
HTTP_HOST = os.getenv("GOOGLE_SHEETS_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_SHEETS_HTTP_PORT", "8015"))
HTTP_PATH = os.getenv("GOOGLE_SHEETS_HTTP_PATH", "/mcp")

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

class GoogleSheetsClient:
    """Client để tương tác với Google Sheets API"""

    def __init__(self):
        # Không khởi tạo service cố định, sẽ tạo mới mỗi lần với token từ request
        pass

    def _create_service_with_token(self, access_token: str):
        """Tạo Google Sheets service với access token từ client"""
        try:
            # Tạo credentials từ access token
            creds = credentials.AccessTokenCredentials(access_token)

            # Tạo service với credentials
            service = build('sheets', 'v4', credentials=creds)
            return service

        except Exception as e:
            raise Exception(f"Lỗi tạo Google Sheets service với token: {str(e)}")

    def _get_service(self):
        """Lấy service với token từ request headers"""
        token = extract_bearer_token_from_request()
        if not token:
            raise Exception("Không tìm thấy Bearer token trong request headers. Vui lòng cung cấp Google OAuth2 access token.")

        return self._create_service_with_token(token)

    async def create_spreadsheet(self, title: str, sheets: List[str] = None) -> Dict:
        """Tạo spreadsheet mới"""
        service = self._get_service()

        body = {
            'properties': {
                'title': title
            }
        }

        if sheets:
            body['sheets'] = []
            for sheet_title in sheets:
                body['sheets'].append({
                    'properties': {
                        'title': sheet_title
                    }
                })

        try:
            result = service.spreadsheets().create(body=body).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi tạo spreadsheet: {str(e)}")

    async def get_spreadsheet(self, spreadsheet_id: str) -> Dict:
        """Lấy thông tin spreadsheet"""
        service = self._get_service()

        try:
            result = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi lấy spreadsheet: {str(e)}")

    async def get_values(self, spreadsheet_id: str, range_name: str) -> Dict:
        """Đọc dữ liệu từ range"""
        service = self._get_service()

        try:
            result = service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi đọc dữ liệu: {str(e)}")

    async def update_values(self, spreadsheet_id: str, range_name: str,
                          values: List[List], value_input_option: str = "RAW") -> Dict:
        """Cập nhật dữ liệu vào range"""
        service = self._get_service()

        body = {
            'values': values
        }

        try:
            result = service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                body=body
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi cập nhật dữ liệu: {str(e)}")

    async def batch_update_values(self, spreadsheet_id: str,
                                data: List[Dict], value_input_option: str = "RAW") -> Dict:
        """Batch update nhiều ranges"""
        service = self._get_service()

        body = {
            'valueInputOption': value_input_option,
            'data': data
        }

        try:
            result = service.spreadsheets().values().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=body
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi batch update: {str(e)}")

    async def append_values(self, spreadsheet_id: str, range_name: str,
                          values: List[List], value_input_option: str = "RAW") -> Dict:
        """Thêm dữ liệu vào cuối range"""
        service = self._get_service()

        body = {
            'values': values
        }

        try:
            result = service.spreadsheets().values().append(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                body=body
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi append dữ liệu: {str(e)}")

    async def clear_values(self, spreadsheet_id: str, range_name: str) -> Dict:
        """Xóa dữ liệu trong range"""
        service = self._get_service()

        try:
            result = service.spreadsheets().values().clear(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi xóa dữ liệu: {str(e)}")

    async def batch_update_spreadsheet(self, spreadsheet_id: str, requests: List[Dict]) -> Dict:
        """Batch update spreadsheet với các requests"""
        service = self._get_service()

        body = {
            'requests': requests
        }

        try:
            result = service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=body
            ).execute()
            return result
        except HttpError as e:
            raise Exception(f"Lỗi batch update spreadsheet: {str(e)}")

# Khởi tạo Google Sheets client
sheets_client = GoogleSheetsClient()

# Khởi tạo MCP server
mcp = FastMCP("Google-Sheets-Server")

# FastMCP tự động handle tools/list từ các @mcp.tool() decorators
# Không cần implement list_tools manually

@mcp.tool(description="Tạo spreadsheet mới")
async def create_spreadsheet_tool(
    ctx,
    title: str,
    sheets: str = ""
) -> str:
    """
    Tạo Google Spreadsheet mới

    Args:
        title: Tên của spreadsheet
        sheets: Danh sách tên sheets cách nhau bởi dấu phẩy (tùy chọn)

    Returns:
        JSON string chứa thông tin spreadsheet đã tạo
    """
    try:
        # Extract Bearer token từ request headers
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🔑 Sử dụng Bearer token cho Google Sheets API: {token[:20]}...")
        await ctx.info(f"📊 Tạo spreadsheet với title: {title}")

        sheet_names = [s.strip() for s in sheets.split(",") if s.strip()] if sheets else None
        if sheet_names:
            await ctx.info(f"📋 Sheets sẽ tạo: {sheet_names}")

        result = await sheets_client.create_spreadsheet(title, sheet_names)

        # Log thông tin spreadsheet đã tạo
        spreadsheet_id = result.get('spreadsheetId', 'Unknown')
        spreadsheet_url = result.get('spreadsheetUrl', 'Unknown')
        await ctx.info(f"✅ Đã tạo spreadsheet thành công - ID: {spreadsheet_id}")
        await ctx.info(f"🔗 URL: {spreadsheet_url}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo spreadsheet: {str(e)}")
        return f"❌ Lỗi tạo spreadsheet: {str(e)}"

@mcp.tool(description="Lấy thông tin về spreadsheet")
async def get_spreadsheet_info_tool(
    ctx,
    spreadsheet_id: str
) -> str:
    """
    Lấy thông tin chi tiết về spreadsheet

    Args:
        spreadsheet_id: ID của spreadsheet

    Returns:
        JSON string chứa thông tin spreadsheet
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📊 Lấy thông tin spreadsheet: {spreadsheet_id}")

        result = await sheets_client.get_spreadsheet(spreadsheet_id)

        # Log thông tin cơ bản
        title = result.get('properties', {}).get('title', 'Unknown')
        sheets_count = len(result.get('sheets', []))
        await ctx.info(f"✅ Đã lấy thông tin spreadsheet: {title} ({sheets_count} sheets)")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy thông tin spreadsheet: {str(e)}")
        return f"❌ Lỗi lấy thông tin spreadsheet: {str(e)}"

@mcp.tool(description="Đọc dữ liệu từ sheet")
async def read_sheet_data_tool(
    ctx,
    spreadsheet_id: str,
    range_name: str
) -> str:
    """
    Đọc dữ liệu từ sheet

    Args:
        spreadsheet_id: ID của spreadsheet
        range_name: Range cần đọc (ví dụ: "Sheet1!A1:C10")

    Returns:
        JSON string chứa dữ liệu đã đọc
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📖 Đọc dữ liệu từ {spreadsheet_id}, range: {range_name}")

        result = await sheets_client.get_values(spreadsheet_id, range_name)

        # Log thông tin về dữ liệu đã đọc
        values = result.get('values', [])
        rows_count = len(values)
        cols_count = max(len(row) for row in values) if values else 0
        await ctx.info(f"✅ Đã đọc {rows_count} hàng, {cols_count} cột từ range {range_name}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi đọc dữ liệu: {str(e)}")
        return f"❌ Lỗi đọc dữ liệu: {str(e)}"

@mcp.tool(description="Ghi dữ liệu vào sheet")
async def write_sheet_data_tool(
    ctx,
    spreadsheet_id: str,
    range_name: str,
    values_json: str,
    value_input_option: str = "RAW"
) -> str:
    """
    Ghi dữ liệu vào sheet

    Args:
        spreadsheet_id: ID của spreadsheet
        range_name: Range cần ghi (ví dụ: "Sheet1!A1:C3")
        values_json: Dữ liệu dạng JSON array 2D (ví dụ: [["A1", "B1"], ["A2", "B2"]])
        value_input_option: Cách xử lý input ("RAW" hoặc "USER_ENTERED")

    Returns:
        JSON string chứa kết quả ghi dữ liệu
    """
    try:
        token = extract_bearer_token_from_request()
        await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

        if not token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"✏️ Ghi dữ liệu vào {spreadsheet_id}, range: {range_name}")

        values = json.loads(values_json)
        rows_count = len(values)
        cols_count = max(len(row) for row in values) if values else 0
        await ctx.info(f"📝 Dữ liệu: {rows_count} hàng, {cols_count} cột, mode: {value_input_option}")

        result = await sheets_client.update_values(
            spreadsheet_id, range_name, values, value_input_option
        )

        updated_cells = result.get('updatedCells', 0)
        await ctx.info(f"✅ Đã cập nhật {updated_cells} cells thành công")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        await ctx.error("❌ Lỗi: values_json không đúng định dạng JSON")
        return "❌ Lỗi: values_json không đúng định dạng JSON"
    except Exception as e:
        await ctx.error(f"❌ Lỗi ghi dữ liệu: {str(e)}")
        return f"❌ Lỗi ghi dữ liệu: {str(e)}"

@mcp.tool(description="Append dữ liệu vào cuối sheet")
async def append_sheet_data_tool(
    spreadsheet_id: str,
    range_name: str,
    values_json: str,
    value_input_option: str = "RAW"
) -> str:
    """
    Thêm dữ liệu vào cuối sheet

    Args:
        spreadsheet_id: ID của spreadsheet
        range_name: Range để append (ví dụ: "Sheet1!A:C")
        values_json: Dữ liệu dạng JSON array 2D
        value_input_option: Cách xử lý input ("RAW" hoặc "USER_ENTERED")

    Returns:
        JSON string chứa kết quả append dữ liệu
    """
    try:
        values = json.loads(values_json)
        result = await sheets_client.append_values(
            spreadsheet_id, range_name, values, value_input_option
        )
        return json.dumps(result, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        return "❌ Lỗi: values_json không đúng định dạng JSON"
    except Exception as e:
        return f"❌ Lỗi append dữ liệu: {str(e)}"

@mcp.tool(description="Xóa dữ liệu trong range")
async def clear_sheet_data_tool(
    spreadsheet_id: str,
    range_name: str
) -> str:
    """
    Xóa dữ liệu trong range

    Args:
        spreadsheet_id: ID của spreadsheet
        range_name: Range cần xóa (ví dụ: "Sheet1!A1:C10")

    Returns:
        JSON string chứa kết quả xóa dữ liệu
    """
    try:
        result = await sheets_client.clear_values(spreadsheet_id, range_name)
        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        return f"❌ Lỗi xóa dữ liệu: {str(e)}"

@mcp.tool(description="Thực hiện nhiều operations cùng lúc")
async def batch_update_sheet_data_tool(
    spreadsheet_id: str,
    data_json: str,
    value_input_option: str = "RAW"
) -> str:
    """
    Batch update nhiều ranges cùng lúc

    Args:
        spreadsheet_id: ID của spreadsheet
        data_json: JSON array chứa các object với range và values
                  Ví dụ: [{"range": "Sheet1!A1:B2", "values": [["A1", "B1"], ["A2", "B2"]]}]
        value_input_option: Cách xử lý input ("RAW" hoặc "USER_ENTERED")

    Returns:
        JSON string chứa kết quả batch update
    """
    try:
        data = json.loads(data_json)
        result = await sheets_client.batch_update_values(
            spreadsheet_id, data, value_input_option
        )
        return json.dumps(result, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        return "❌ Lỗi: data_json không đúng định dạng JSON"
    except Exception as e:
        return f"❌ Lỗi batch update: {str(e)}"

# Thêm các methods nâng cao cho spreadsheet management
@mcp.tool(description="Thêm sheet mới vào spreadsheet")
async def add_sheet_tool(
    spreadsheet_id: str,
    sheet_title: str,
    rows: int = 1000,
    columns: int = 26
) -> str:
    """
    Thêm sheet mới vào spreadsheet

    Args:
        spreadsheet_id: ID của spreadsheet
        sheet_title: Tên của sheet mới
        rows: Số hàng (mặc định 1000)
        columns: Số cột (mặc định 26)

    Returns:
        JSON string chứa kết quả thêm sheet
    """
    try:
        if not sheets_client.service:
            raise Exception("Google Sheets service chưa được khởi tạo")

        body = {
            'requests': [{
                'addSheet': {
                    'properties': {
                        'title': sheet_title,
                        'gridProperties': {
                            'rowCount': rows,
                            'columnCount': columns
                        }
                    }
                }
            }]
        }

        result = sheets_client.service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body=body
        ).execute()

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        return f"❌ Lỗi thêm sheet: {str(e)}"

@mcp.tool(description="Xóa sheet khỏi spreadsheet")
async def delete_sheet_tool(
    spreadsheet_id: str,
    sheet_id: int
) -> str:
    """
    Xóa sheet khỏi spreadsheet

    Args:
        spreadsheet_id: ID của spreadsheet
        sheet_id: ID của sheet cần xóa (không phải tên)

    Returns:
        JSON string chứa kết quả xóa sheet
    """
    try:
        if not sheets_client.service:
            raise Exception("Google Sheets service chưa được khởi tạo")

        body = {
            'requests': [{
                'deleteSheet': {
                    'sheetId': sheet_id
                }
            }]
        }

        result = sheets_client.service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body=body
        ).execute()

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        return f"❌ Lỗi xóa sheet: {str(e)}"

@mcp.tool(description="Format cells trong range")
async def format_cells_tool(
    spreadsheet_id: str,
    sheet_id: int,
    start_row: int,
    end_row: int,
    start_col: int,
    end_col: int,
    format_json: str
) -> str:
    """
    Format cells trong range

    Args:
        spreadsheet_id: ID của spreadsheet
        sheet_id: ID của sheet
        start_row: Hàng bắt đầu (0-indexed)
        end_row: Hàng kết thúc (0-indexed, exclusive)
        start_col: Cột bắt đầu (0-indexed)
        end_col: Cột kết thúc (0-indexed, exclusive)
        format_json: JSON format object (ví dụ: {"backgroundColor": {"red": 1.0}})

    Returns:
        JSON string chứa kết quả format
    """
    try:
        if not sheets_client.service:
            raise Exception("Google Sheets service chưa được khởi tạo")

        cell_format = json.loads(format_json)

        body = {
            'requests': [{
                'repeatCell': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': start_row,
                        'endRowIndex': end_row,
                        'startColumnIndex': start_col,
                        'endColumnIndex': end_col
                    },
                    'cell': {
                        'userEnteredFormat': cell_format
                    },
                    'fields': 'userEnteredFormat'
                }
            }]
        }

        service = sheets_client._get_service()
        result = service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body=body
        ).execute()

        return json.dumps(result, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        return "❌ Lỗi: format_json không đúng định dạng JSON"
    except Exception as e:
        return f"❌ Lỗi format cells: {str(e)}"

# Health check endpoint
@mcp.tool(description="Health check cho Google Sheets server")
async def health_check() -> str:
    """Health check endpoint"""
    return json.dumps({
        "status": "healthy",
        "service": "Google Sheets MCP Server",
        "port": HTTP_PORT,
        "transport": "streamable-http"
    }, ensure_ascii=False, indent=2)

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động Google Sheets MCP Server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path=HTTP_PATH
            )

        elif transport == "sse":
            print(f"🚀 Khởi động Google Sheets MCP Server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động Google Sheets MCP Server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def main():
    """Hàm main để chạy server"""
    try:
        print("=" * 70)
        print("📊 Khởi động Google Sheets MCP Server")
        print("=" * 70)
        print("📋 Transport: Streamable HTTP (FastMCP)")
        print(f"🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print("🔧 Google Sheets API: v4")
        print("🔑 Authentication: Bearer Token từ client headers")
        print("📝 Lưu ý: Client cần cung cấp Google OAuth2 access token trong Authorization header")
        print()

        print("=" * 70)
        print("🚀 Đang khởi động server...")

        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()