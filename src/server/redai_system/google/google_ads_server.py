#!/usr/bin/env python3
"""
Google Ads MCP Server

MCP Server cho Google Ads API sử dụng FastMCP framework với HTTP transport.
Cung cấp các tools để quản lý Google Ads campaigns, ad groups, keywords, và reports.

Author: RedAI Team
Date: 2024-12-28
"""

import asyncio
import json
import os
import sys
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
sys.path.insert(0, project_root)

from fastmcp import FastMCP

# Google Ads imports
try:
    from google.ads.googleads.client import GoogleAdsClient
    from google.ads.googleads.errors import GoogleAdsException
    from google.oauth2.credentials import Credentials
    import yaml
except ImportError as e:
    print(f"❌ Lỗi import Google Ads libraries: {e}")
    print("💡 Cài đặt: pip install google-ads PyYAML")
    sys.exit(1)

import httpx

# C<PERSON>u hình server
HTTP_HOST = os.getenv("GOOGLE_ADS_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GOOGLE_ADS_HTTP_PORT", "8020"))
HTTP_PATH = os.getenv("GOOGLE_ADS_HTTP_PATH", "/mcp")

# Cấu hình SSE fallback
SSE_HOST = os.getenv("GOOGLE_ADS_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GOOGLE_ADS_SSE_PORT", "8120"))
SSE_PATH = os.getenv("GOOGLE_ADS_SSE_PATH", "/sse")

# Transport preference: http hoặc sse
TRANSPORT_TYPE = os.getenv("GOOGLE_ADS_TRANSPORT", "http").lower()  # http hoặc sse

def get_http_request():
    """
    Lấy HTTP request object từ FastMCP context
    """
    try:
        import inspect
        frame = inspect.currentframe()
        while frame:
            if 'request' in frame.f_locals:
                return frame.f_locals['request']
            frame = frame.f_back
        return None
    except Exception:
        return None

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

class GoogleAdsClientWrapper:
    """Client wrapper để tương tác với Google Ads API"""

    def __init__(self):
        self.client = None
        self.customer_id = None

    def _get_client(self, access_token: str):
        """Tạo Google Ads client với access token"""
        try:
            # Tạo credentials từ access token
            credentials = Credentials(token=access_token)

            # Tạo config cho Google Ads client
            config = {
                'developer_token': os.getenv('GOOGLE_ADS_DEVELOPER_TOKEN', ''),
                'use_proto_plus': True,
                'login_customer_id': os.getenv('GOOGLE_ADS_LOGIN_CUSTOMER_ID', ''),
            }

            # Tạo client từ config
            client = GoogleAdsClient.load_from_dict(config)

            # Set OAuth2 credentials
            client.oauth2_credentials = credentials

            self.client = client
            return client

        except Exception as e:
            raise Exception(f"Lỗi tạo Google Ads client: {str(e)}")
    
    async def get_campaigns(self, access_token: str, customer_id: str) -> Dict:
        """Lấy danh sách campaigns"""
        client = self._get_client(access_token)

        try:
            ga_service = client.get_service("GoogleAdsService")

            query = """
                SELECT
                    campaign.id,
                    campaign.name,
                    campaign.status,
                    campaign.advertising_channel_type,
                    campaign.start_date,
                    campaign.end_date,
                    campaign_budget.amount_micros
                FROM campaign
                ORDER BY campaign.id
            """

            # Search request
            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            response = ga_service.search(request=search_request)

            campaigns = []
            for row in response:
                campaign = row.campaign
                budget = row.campaign_budget if hasattr(row, 'campaign_budget') else None

                campaigns.append({
                    'id': str(campaign.id),
                    'name': campaign.name,
                    'status': campaign.status.name,
                    'type': campaign.advertising_channel_type.name,
                    'start_date': campaign.start_date,
                    'end_date': campaign.end_date,
                    'budget_micros': str(budget.amount_micros) if budget else None
                })

            return {
                'customer_id': customer_id,
                'campaigns': campaigns,
                'total_count': len(campaigns)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")
    
    async def get_ad_groups(self, access_token: str, customer_id: str, campaign_id: str = None) -> Dict:
        """Lấy danh sách ad groups"""
        client = self._get_client(access_token)

        try:
            ga_service = client.get_service("GoogleAdsService")

            query = """
                SELECT
                    ad_group.id,
                    ad_group.name,
                    ad_group.status,
                    ad_group.type,
                    campaign.id,
                    campaign.name
                FROM ad_group
            """

            if campaign_id:
                query += f" WHERE campaign.id = {campaign_id}"

            query += " ORDER BY ad_group.id"

            # Search request
            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            response = ga_service.search(request=search_request)

            ad_groups = []
            for row in response:
                ad_group = row.ad_group
                campaign = row.campaign

                ad_groups.append({
                    'id': str(ad_group.id),
                    'name': ad_group.name,
                    'status': ad_group.status.name,
                    'type': ad_group.type_.name,
                    'campaign_id': str(campaign.id),
                    'campaign_name': campaign.name
                })

            return {
                'customer_id': customer_id,
                'campaign_id': campaign_id,
                'ad_groups': ad_groups,
                'total_count': len(ad_groups)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")
    
    async def get_keywords(self, access_token: str, customer_id: str, ad_group_id: str = None) -> Dict:
        """Lấy danh sách keywords"""
        client = self._get_client(access_token)

        try:
            ga_service = client.get_service("GoogleAdsService")

            query = """
                SELECT
                    ad_group_criterion.criterion_id,
                    ad_group_criterion.keyword.text,
                    ad_group_criterion.keyword.match_type,
                    ad_group_criterion.status,
                    ad_group.id,
                    ad_group.name,
                    campaign.id,
                    campaign.name
                FROM keyword_view
            """

            if ad_group_id:
                query += f" WHERE ad_group.id = {ad_group_id}"

            query += " ORDER BY ad_group_criterion.criterion_id"

            # Search request
            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            response = ga_service.search(request=search_request)

            keywords = []
            for row in response:
                criterion = row.ad_group_criterion
                keyword = criterion.keyword
                ad_group = row.ad_group
                campaign = row.campaign

                keywords.append({
                    'criterion_id': str(criterion.criterion_id),
                    'text': keyword.text,
                    'match_type': keyword.match_type.name,
                    'status': criterion.status.name,
                    'ad_group_id': str(ad_group.id),
                    'ad_group_name': ad_group.name,
                    'campaign_id': str(campaign.id),
                    'campaign_name': campaign.name
                })

            return {
                'customer_id': customer_id,
                'ad_group_id': ad_group_id,
                'keywords': keywords,
                'total_count': len(keywords)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def get_campaign_performance(self, access_token: str, customer_id: str,
                                     start_date: str = "2024-01-01",
                                     end_date: str = "2024-12-31") -> Dict:
        """Lấy performance report cho campaigns"""
        client = self._get_client(access_token)

        try:
            ga_service = client.get_service("GoogleAdsService")

            query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    campaign.status,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.cost_micros,
                    metrics.ctr,
                    metrics.average_cpc,
                    metrics.conversions,
                    metrics.conversion_rate
                FROM campaign
                WHERE segments.date BETWEEN '{start_date}' AND '{end_date}'
                ORDER BY metrics.cost_micros DESC
            """

            # Search request
            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            response = ga_service.search(request=search_request)

            performance_data = []
            for row in response:
                campaign = row.campaign
                metrics = row.metrics

                performance_data.append({
                    'campaign_id': str(campaign.id),
                    'campaign_name': campaign.name,
                    'status': campaign.status.name,
                    'impressions': str(metrics.impressions),
                    'clicks': str(metrics.clicks),
                    'cost_micros': str(metrics.cost_micros),
                    'ctr': float(metrics.ctr),
                    'average_cpc': str(metrics.average_cpc),
                    'conversions': float(metrics.conversions),
                    'conversion_rate': float(metrics.conversion_rate)
                })

            return {
                'customer_id': customer_id,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'performance_data': performance_data,
                'total_count': len(performance_data)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def create_campaign(self, access_token: str, customer_id: str,
                            campaign_name: str, budget_amount_micros: str,
                            advertising_channel_type: str = "SEARCH") -> Dict:
        """Tạo campaign mới"""
        client = self._get_client(access_token)

        try:
            # Tạo campaign budget trước
            campaign_budget_service = client.get_service("CampaignBudgetService")
            campaign_budget_operation = client.get_type("CampaignBudgetOperation")

            campaign_budget = campaign_budget_operation.create
            campaign_budget.name = f"Budget for {campaign_name}"
            campaign_budget.amount_micros = int(budget_amount_micros)
            campaign_budget.delivery_method = client.enums.BudgetDeliveryMethodEnum.STANDARD

            budget_response = campaign_budget_service.mutate_campaign_budgets(
                customer_id=customer_id, operations=[campaign_budget_operation]
            )
            budget_resource_name = budget_response.results[0].resource_name

            # Tạo campaign
            campaign_service = client.get_service("CampaignService")
            campaign_operation = client.get_type("CampaignOperation")

            campaign = campaign_operation.create
            campaign.name = campaign_name
            campaign.advertising_channel_type = getattr(
                client.enums.AdvertisingChannelTypeEnum, advertising_channel_type
            )
            campaign.status = client.enums.CampaignStatusEnum.PAUSED
            campaign.campaign_budget = budget_resource_name

            # Bidding strategy
            campaign.manual_cpc.enhanced_cpc_enabled = True

            campaign_response = campaign_service.mutate_campaigns(
                customer_id=customer_id, operations=[campaign_operation]
            )

            return {
                'customer_id': customer_id,
                'campaign_resource_name': campaign_response.results[0].resource_name,
                'budget_resource_name': budget_resource_name,
                'campaign_name': campaign_name,
                'budget_amount_micros': budget_amount_micros,
                'status': 'PAUSED'
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def get_recommendations(self, access_token: str, customer_id: str) -> Dict:
        """Lấy recommendations cho tối ưu hóa"""
        client = self._get_client(access_token)

        try:
            recommendation_service = client.get_service("RecommendationService")

            # Get recommendations
            request = client.get_type("ListRecommendationsRequest")
            request.customer_id = customer_id

            response = recommendation_service.list_recommendations(request=request)

            recommendations = []
            for recommendation in response.recommendations:
                recommendations.append({
                    'resource_name': recommendation.resource_name,
                    'type': recommendation.type_.name,
                    'impact': {
                        'base_metrics': {
                            'impressions': str(recommendation.impact.base_metrics.impressions),
                            'clicks': str(recommendation.impact.base_metrics.clicks),
                            'cost_micros': str(recommendation.impact.base_metrics.cost_micros)
                        } if recommendation.impact.base_metrics else None,
                        'potential_metrics': {
                            'impressions': str(recommendation.impact.potential_metrics.impressions),
                            'clicks': str(recommendation.impact.potential_metrics.clicks),
                            'cost_micros': str(recommendation.impact.potential_metrics.cost_micros)
                        } if recommendation.impact.potential_metrics else None
                    }
                })

            return {
                'customer_id': customer_id,
                'recommendations': recommendations,
                'total_count': len(recommendations)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def create_ad_group(self, access_token: str, customer_id: str,
                            campaign_id: str, ad_group_name: str,
                            cpc_bid_micros: str = "1000000") -> Dict:
        """Tạo ad group mới"""
        client = self._get_client(access_token)

        try:
            ad_group_service = client.get_service("AdGroupService")
            ad_group_operation = client.get_type("AdGroupOperation")

            ad_group = ad_group_operation.create
            ad_group.name = ad_group_name
            ad_group.campaign = client.get_service("CampaignService").campaign_path(customer_id, campaign_id)
            ad_group.status = client.enums.AdGroupStatusEnum.ENABLED
            ad_group.type_ = client.enums.AdGroupTypeEnum.SEARCH_STANDARD
            ad_group.cpc_bid_micros = int(cpc_bid_micros)

            response = ad_group_service.mutate_ad_groups(
                customer_id=customer_id, operations=[ad_group_operation]
            )

            return {
                'customer_id': customer_id,
                'campaign_id': campaign_id,
                'ad_group_resource_name': response.results[0].resource_name,
                'ad_group_name': ad_group_name,
                'cpc_bid_micros': cpc_bid_micros,
                'status': 'ENABLED'
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def create_keyword(self, access_token: str, customer_id: str,
                           ad_group_id: str, keyword_text: str,
                           match_type: str = "BROAD", cpc_bid_micros: str = "1000000") -> Dict:
        """Tạo keyword mới trong ad group"""
        client = self._get_client(access_token)

        try:
            ad_group_criterion_service = client.get_service("AdGroupCriterionService")
            ad_group_criterion_operation = client.get_type("AdGroupCriterionOperation")

            ad_group_criterion = ad_group_criterion_operation.create
            ad_group_criterion.ad_group = client.get_service("AdGroupService").ad_group_path(customer_id, ad_group_id)
            ad_group_criterion.status = client.enums.AdGroupCriterionStatusEnum.ENABLED

            # Set keyword
            ad_group_criterion.keyword.text = keyword_text
            ad_group_criterion.keyword.match_type = getattr(
                client.enums.KeywordMatchTypeEnum, match_type
            )

            # Set bid
            ad_group_criterion.cpc_bid_micros = int(cpc_bid_micros)

            response = ad_group_criterion_service.mutate_ad_group_criteria(
                customer_id=customer_id, operations=[ad_group_criterion_operation]
            )

            return {
                'customer_id': customer_id,
                'ad_group_id': ad_group_id,
                'criterion_resource_name': response.results[0].resource_name,
                'keyword_text': keyword_text,
                'match_type': match_type,
                'cpc_bid_micros': cpc_bid_micros,
                'status': 'ENABLED'
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def create_responsive_search_ad(self, access_token: str, customer_id: str,
                                        ad_group_id: str, headlines: List[str],
                                        descriptions: List[str], final_urls: List[str]) -> Dict:
        """Tạo Responsive Search Ad"""
        client = self._get_client(access_token)

        try:
            ad_group_ad_service = client.get_service("AdGroupAdService")
            ad_group_ad_operation = client.get_type("AdGroupAdOperation")

            ad_group_ad = ad_group_ad_operation.create
            ad_group_ad.ad_group = client.get_service("AdGroupService").ad_group_path(customer_id, ad_group_id)
            ad_group_ad.status = client.enums.AdGroupAdStatusEnum.ENABLED

            # Create Responsive Search Ad
            ad_group_ad.ad.responsive_search_ad.headlines.extend([
                client.get_type("AdTextAsset", text=headline) for headline in headlines[:15]  # Max 15 headlines
            ])
            ad_group_ad.ad.responsive_search_ad.descriptions.extend([
                client.get_type("AdTextAsset", text=description) for description in descriptions[:4]  # Max 4 descriptions
            ])
            ad_group_ad.ad.final_urls.extend(final_urls)

            response = ad_group_ad_service.mutate_ad_group_ads(
                customer_id=customer_id, operations=[ad_group_ad_operation]
            )

            return {
                'customer_id': customer_id,
                'ad_group_id': ad_group_id,
                'ad_resource_name': response.results[0].resource_name,
                'ad_type': 'RESPONSIVE_SEARCH_AD',
                'headlines_count': len(headlines),
                'descriptions_count': len(descriptions),
                'final_urls': final_urls,
                'status': 'ENABLED'
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def get_ads(self, access_token: str, customer_id: str, ad_group_id: str = None) -> Dict:
        """Lấy danh sách ads"""
        client = self._get_client(access_token)

        try:
            ga_service = client.get_service("GoogleAdsService")

            query = """
                SELECT
                    ad_group_ad.ad.id,
                    ad_group_ad.ad.type,
                    ad_group_ad.status,
                    ad_group_ad.ad.responsive_search_ad.headlines,
                    ad_group_ad.ad.responsive_search_ad.descriptions,
                    ad_group_ad.ad.final_urls,
                    ad_group.id,
                    ad_group.name,
                    campaign.id,
                    campaign.name
                FROM ad_group_ad
            """

            if ad_group_id:
                query += f" WHERE ad_group.id = {ad_group_id}"

            query += " ORDER BY ad_group_ad.ad.id"

            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            response = ga_service.search(request=search_request)

            ads = []
            for row in response:
                ad_group_ad = row.ad_group_ad
                ad = ad_group_ad.ad
                ad_group = row.ad_group
                campaign = row.campaign

                ad_data = {
                    'ad_id': str(ad.id),
                    'ad_type': ad.type_.name,
                    'status': ad_group_ad.status.name,
                    'ad_group_id': str(ad_group.id),
                    'ad_group_name': ad_group.name,
                    'campaign_id': str(campaign.id),
                    'campaign_name': campaign.name,
                    'final_urls': list(ad.final_urls)
                }

                # Add specific ad type data
                if ad.type_.name == 'RESPONSIVE_SEARCH_AD':
                    ad_data['headlines'] = [headline.text for headline in ad.responsive_search_ad.headlines]
                    ad_data['descriptions'] = [desc.text for desc in ad.responsive_search_ad.descriptions]

                ads.append(ad_data)

            return {
                'customer_id': customer_id,
                'ad_group_id': ad_group_id,
                'ads': ads,
                'total_count': len(ads)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def get_keyword_ideas(self, access_token: str, customer_id: str,
                              keywords: List[str], language_id: str = "1000",
                              location_ids: List[str] = None) -> Dict:
        """Lấy keyword ideas từ Keyword Planner"""
        client = self._get_client(access_token)

        try:
            keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")
            request = client.get_type("GenerateKeywordIdeasRequest")

            request.customer_id = customer_id
            request.language = client.get_service("GoogleAdsService").language_constant_path(language_id)

            # Set location targeting
            if location_ids:
                request.geo_target_constants.extend([
                    client.get_service("GoogleAdsService").geo_target_constant_path(location_id)
                    for location_id in location_ids
                ])
            else:
                # Default to US
                request.geo_target_constants.append(
                    client.get_service("GoogleAdsService").geo_target_constant_path("2840")
                )

            # Set keyword seed
            request.keyword_seed.keywords.extend(keywords)

            response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

            keyword_ideas = []
            for idea in response.results:
                keyword_ideas.append({
                    'keyword_text': idea.text,
                    'avg_monthly_searches': str(idea.keyword_idea_metrics.avg_monthly_searches) if idea.keyword_idea_metrics else None,
                    'competition': idea.keyword_idea_metrics.competition.name if idea.keyword_idea_metrics else None,
                    'low_top_of_page_bid_micros': str(idea.keyword_idea_metrics.low_top_of_page_bid_micros) if idea.keyword_idea_metrics else None,
                    'high_top_of_page_bid_micros': str(idea.keyword_idea_metrics.high_top_of_page_bid_micros) if idea.keyword_idea_metrics else None
                })

            return {
                'customer_id': customer_id,
                'seed_keywords': keywords,
                'language_id': language_id,
                'location_ids': location_ids or ["2840"],
                'keyword_ideas': keyword_ideas,
                'total_count': len(keyword_ideas)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def get_bidding_strategies(self, access_token: str, customer_id: str) -> Dict:
        """Lấy danh sách bidding strategies"""
        client = self._get_client(access_token)

        try:
            ga_service = client.get_service("GoogleAdsService")

            query = """
                SELECT
                    bidding_strategy.id,
                    bidding_strategy.name,
                    bidding_strategy.type,
                    bidding_strategy.status,
                    bidding_strategy.target_cpa.target_cpa_micros,
                    bidding_strategy.target_roas.target_roas,
                    bidding_strategy.target_spend.target_spend_micros
                FROM bidding_strategy
                ORDER BY bidding_strategy.id
            """

            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            response = ga_service.search(request=search_request)

            bidding_strategies = []
            for row in response:
                strategy = row.bidding_strategy

                strategy_data = {
                    'id': str(strategy.id),
                    'name': strategy.name,
                    'type': strategy.type_.name,
                    'status': strategy.status.name
                }

                # Add type-specific data
                if strategy.type_.name == 'TARGET_CPA':
                    strategy_data['target_cpa_micros'] = str(strategy.target_cpa.target_cpa_micros)
                elif strategy.type_.name == 'TARGET_ROAS':
                    strategy_data['target_roas'] = float(strategy.target_roas.target_roas)
                elif strategy.type_.name == 'TARGET_SPEND':
                    strategy_data['target_spend_micros'] = str(strategy.target_spend.target_spend_micros)

                bidding_strategies.append(strategy_data)

            return {
                'customer_id': customer_id,
                'bidding_strategies': bidding_strategies,
                'total_count': len(bidding_strategies)
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

    async def create_target_cpa_bidding_strategy(self, access_token: str, customer_id: str,
                                               strategy_name: str, target_cpa_micros: str) -> Dict:
        """Tạo Target CPA bidding strategy"""
        client = self._get_client(access_token)

        try:
            bidding_strategy_service = client.get_service("BiddingStrategyService")
            bidding_strategy_operation = client.get_type("BiddingStrategyOperation")

            bidding_strategy = bidding_strategy_operation.create
            bidding_strategy.name = strategy_name
            bidding_strategy.target_cpa.target_cpa_micros = int(target_cpa_micros)

            response = bidding_strategy_service.mutate_bidding_strategies(
                customer_id=customer_id, operations=[bidding_strategy_operation]
            )

            return {
                'customer_id': customer_id,
                'strategy_resource_name': response.results[0].resource_name,
                'strategy_name': strategy_name,
                'type': 'TARGET_CPA',
                'target_cpa_micros': target_cpa_micros
            }

        except GoogleAdsException as ex:
            error_details = []
            for error in ex.failure.errors:
                error_details.append({
                    'error_code': error.error_code,
                    'message': error.message
                })
            raise Exception(f"Google Ads API error: {error_details}")

# Khởi tạo Google Ads client wrapper
ads_client = GoogleAdsClientWrapper()

# Khởi tạo MCP server
mcp = FastMCP("Google-Ads-Server")

@mcp._mcp_list_tools()
async def list_tools() -> List[Tool]:
    """
    List all available tools in Google Ads MCP Server
    Returns list of Tool objects according to MCP protocol
    """
    return [
        Tool(
            name="get_campaigns_tool",
            description="Lấy danh sách campaigns trong Google Ads",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID (không có dấu gạch ngang)"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="get_ad_groups_tool",
            description="Lấy danh sách ad groups",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "campaign_id": {"type": "string", "description": "Campaign ID để filter (tùy chọn)"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="get_keywords_tool",
            description="Lấy danh sách keywords",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "ad_group_id": {"type": "string", "description": "Ad Group ID để filter (tùy chọn)"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="get_campaign_performance_tool",
            description="Lấy performance report cho campaigns",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "start_date": {"type": "string", "description": "Ngày bắt đầu (YYYY-MM-DD)", "default": "2024-01-01"},
                    "end_date": {"type": "string", "description": "Ngày kết thúc (YYYY-MM-DD)", "default": "2024-12-31"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="create_campaign_tool",
            description="Tạo campaign mới trong Google Ads",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "campaign_name": {"type": "string", "description": "Tên campaign"},
                    "budget_amount_micros": {"type": "string", "description": "Budget amount in micros (1 USD = 1,000,000 micros)"},
                    "advertising_channel_type": {"type": "string", "description": "Loại campaign", "default": "SEARCH", "enum": ["SEARCH", "DISPLAY", "SHOPPING", "VIDEO"]}
                },
                "required": ["customer_id", "campaign_name", "budget_amount_micros"]
            }
        ),
        Tool(
            name="get_recommendations_tool",
            description="Lấy recommendations để tối ưu hóa campaigns",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="create_ad_group_tool",
            description="Tạo ad group mới trong campaign",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "campaign_id": {"type": "string", "description": "Campaign ID"},
                    "ad_group_name": {"type": "string", "description": "Tên ad group"},
                    "cpc_bid_micros": {"type": "string", "description": "CPC bid in micros", "default": "1000000"}
                },
                "required": ["customer_id", "campaign_id", "ad_group_name"]
            }
        ),
        Tool(
            name="create_keyword_tool",
            description="Tạo keyword mới trong ad group",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "ad_group_id": {"type": "string", "description": "Ad Group ID"},
                    "keyword_text": {"type": "string", "description": "Keyword text"},
                    "match_type": {"type": "string", "description": "Match type", "default": "BROAD", "enum": ["EXACT", "PHRASE", "BROAD"]},
                    "cpc_bid_micros": {"type": "string", "description": "CPC bid in micros", "default": "1000000"}
                },
                "required": ["customer_id", "ad_group_id", "keyword_text"]
            }
        ),
        Tool(
            name="create_responsive_search_ad_tool",
            description="Tạo Responsive Search Ad",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "ad_group_id": {"type": "string", "description": "Ad Group ID"},
                    "headlines": {"type": "string", "description": "JSON array của headlines (tối đa 15)"},
                    "descriptions": {"type": "string", "description": "JSON array của descriptions (tối đa 4)"},
                    "final_urls": {"type": "string", "description": "JSON array của final URLs"}
                },
                "required": ["customer_id", "ad_group_id", "headlines", "descriptions", "final_urls"]
            }
        ),
        Tool(
            name="get_ads_tool",
            description="Lấy danh sách ads",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "ad_group_id": {"type": "string", "description": "Ad Group ID để filter (tùy chọn)"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="get_keyword_ideas_tool",
            description="Lấy keyword ideas từ Keyword Planner",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "keywords": {"type": "string", "description": "JSON array của seed keywords"},
                    "language_id": {"type": "string", "description": "Language ID", "default": "1000"},
                    "location_ids": {"type": "string", "description": "JSON array của location IDs (tùy chọn)"}
                },
                "required": ["customer_id", "keywords"]
            }
        ),
        Tool(
            name="get_bidding_strategies_tool",
            description="Lấy danh sách bidding strategies",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"}
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="create_target_cpa_bidding_strategy_tool",
            description="Tạo Target CPA bidding strategy",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {"type": "string", "description": "Google Ads Customer ID"},
                    "strategy_name": {"type": "string", "description": "Tên bidding strategy"},
                    "target_cpa_micros": {"type": "string", "description": "Target CPA in micros"}
                },
                "required": ["customer_id", "strategy_name", "target_cpa_micros"]
            }
        )
    ]

@mcp.tool(description="Lấy danh sách campaigns trong Google Ads")
async def get_campaigns_tool(
    ctx,
    customer_id: str
) -> str:
    """
    Lấy danh sách campaigns trong Google Ads account
    
    Args:
        customer_id: Google Ads Customer ID (không có dấu gạch ngang)
    
    Returns:
        JSON string chứa danh sách campaigns
    """
    try:
        # Extract access token từ request headers
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"
        
        await ctx.info(f"📊 Lấy danh sách campaigns cho customer: {customer_id}")
        
        result = await ads_client.get_campaigns(access_token, customer_id)
        
        # Log thông tin cơ bản
        campaigns_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {campaigns_count} campaigns")
        
        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy campaigns: {str(e)}")
        return f"❌ Lỗi lấy campaigns: {str(e)}"

@mcp.tool(description="Lấy danh sách ad groups")
async def get_ad_groups_tool(
    ctx,
    customer_id: str,
    campaign_id: str = ""
) -> str:
    """
    Lấy danh sách ad groups
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📊 Lấy ad groups cho customer: {customer_id}")
        if campaign_id:
            await ctx.info(f"🎯 Filter theo campaign: {campaign_id}")

        result = await ads_client.get_ad_groups(access_token, customer_id, campaign_id or None)

        ad_groups_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {ad_groups_count} ad groups")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ad groups: {str(e)}")
        return f"❌ Lỗi lấy ad groups: {str(e)}"

@mcp.tool(description="Lấy danh sách keywords")
async def get_keywords_tool(
    ctx,
    customer_id: str,
    ad_group_id: str = ""
) -> str:
    """
    Lấy danh sách keywords
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🔑 Lấy keywords cho customer: {customer_id}")
        if ad_group_id:
            await ctx.info(f"🎯 Filter theo ad group: {ad_group_id}")

        result = await ads_client.get_keywords(access_token, customer_id, ad_group_id or None)

        keywords_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {keywords_count} keywords")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy keywords: {str(e)}")
        return f"❌ Lỗi lấy keywords: {str(e)}"

@mcp.tool(description="Lấy performance report cho campaigns")
async def get_campaign_performance_tool(
    ctx,
    customer_id: str,
    start_date: str = "2024-01-01",
    end_date: str = "2024-12-31"
) -> str:
    """
    Lấy performance report cho campaigns
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📊 Lấy performance report cho customer: {customer_id}")
        await ctx.info(f"📅 Thời gian: {start_date} đến {end_date}")

        result = await ads_client.get_campaign_performance(access_token, customer_id, start_date, end_date)

        performance_count = result.get('total_count', 0)
        await ctx.info(f"✅ Trả về {performance_count} campaign performance records")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy performance report: {str(e)}")
        return f"❌ Lỗi lấy performance report: {str(e)}"

@mcp.tool(description="Tạo campaign mới trong Google Ads")
async def create_campaign_tool(
    ctx,
    customer_id: str,
    campaign_name: str,
    budget_amount_micros: str,
    advertising_channel_type: str = "SEARCH"
) -> str:
    """
    Tạo campaign mới trong Google Ads
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🆕 Tạo campaign mới cho customer: {customer_id}")
        await ctx.info(f"📝 Tên campaign: {campaign_name}")
        await ctx.info(f"💰 Budget: {budget_amount_micros} micros")

        result = await ads_client.create_campaign(
            access_token, customer_id, campaign_name, budget_amount_micros, advertising_channel_type
        )

        await ctx.info(f"✅ Tạo campaign thành công: {result.get('campaign_resource_name')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo campaign: {str(e)}")
        return f"❌ Lỗi tạo campaign: {str(e)}"

@mcp.tool(description="Lấy recommendations để tối ưu hóa campaigns")
async def get_recommendations_tool(
    ctx,
    customer_id: str
) -> str:
    """
    Lấy recommendations để tối ưu hóa campaigns
    """
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"💡 Lấy recommendations cho customer: {customer_id}")

        result = await ads_client.get_recommendations(access_token, customer_id)

        recommendations_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {recommendations_count} recommendations")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy recommendations: {str(e)}")
        return f"❌ Lỗi lấy recommendations: {str(e)}"

@mcp.tool(description="Tạo ad group mới trong campaign")
async def create_ad_group_tool(
    ctx,
    customer_id: str,
    campaign_id: str,
    ad_group_name: str,
    cpc_bid_micros: str = "1000000"
) -> str:
    """Tạo ad group mới trong campaign"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🆕 Tạo ad group mới cho campaign: {campaign_id}")
        await ctx.info(f"📝 Tên ad group: {ad_group_name}")

        result = await ads_client.create_ad_group(
            access_token, customer_id, campaign_id, ad_group_name, cpc_bid_micros
        )

        await ctx.info(f"✅ Tạo ad group thành công: {result.get('ad_group_resource_name')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo ad group: {str(e)}")
        return f"❌ Lỗi tạo ad group: {str(e)}"

@mcp.tool(description="Tạo keyword mới trong ad group")
async def create_keyword_tool(
    ctx,
    customer_id: str,
    ad_group_id: str,
    keyword_text: str,
    match_type: str = "BROAD",
    cpc_bid_micros: str = "1000000"
) -> str:
    """Tạo keyword mới trong ad group"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🔑 Tạo keyword mới cho ad group: {ad_group_id}")
        await ctx.info(f"📝 Keyword: {keyword_text} ({match_type})")

        result = await ads_client.create_keyword(
            access_token, customer_id, ad_group_id, keyword_text, match_type, cpc_bid_micros
        )

        await ctx.info(f"✅ Tạo keyword thành công: {result.get('criterion_resource_name')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo keyword: {str(e)}")
        return f"❌ Lỗi tạo keyword: {str(e)}"

@mcp.tool(description="Tạo Responsive Search Ad")
async def create_responsive_search_ad_tool(
    ctx,
    customer_id: str,
    ad_group_id: str,
    headlines: str,
    descriptions: str,
    final_urls: str
) -> str:
    """Tạo Responsive Search Ad"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        # Parse JSON inputs
        import json
        headlines_list = json.loads(headlines)
        descriptions_list = json.loads(descriptions)
        final_urls_list = json.loads(final_urls)

        await ctx.info(f"📝 Tạo Responsive Search Ad cho ad group: {ad_group_id}")
        await ctx.info(f"📰 Headlines: {len(headlines_list)} items")
        await ctx.info(f"📄 Descriptions: {len(descriptions_list)} items")

        result = await ads_client.create_responsive_search_ad(
            access_token, customer_id, ad_group_id, headlines_list, descriptions_list, final_urls_list
        )

        await ctx.info(f"✅ Tạo ad thành công: {result.get('ad_resource_name')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo ad: {str(e)}")
        return f"❌ Lỗi tạo ad: {str(e)}"

@mcp.tool(description="Lấy danh sách ads")
async def get_ads_tool(
    ctx,
    customer_id: str,
    ad_group_id: str = ""
) -> str:
    """Lấy danh sách ads"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"📝 Lấy ads cho customer: {customer_id}")
        if ad_group_id:
            await ctx.info(f"🎯 Filter theo ad group: {ad_group_id}")

        result = await ads_client.get_ads(access_token, customer_id, ad_group_id or None)

        ads_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {ads_count} ads")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy ads: {str(e)}")
        return f"❌ Lỗi lấy ads: {str(e)}"

@mcp.tool(description="Lấy keyword ideas từ Keyword Planner")
async def get_keyword_ideas_tool(
    ctx,
    customer_id: str,
    keywords: str,
    language_id: str = "1000",
    location_ids: str = ""
) -> str:
    """Lấy keyword ideas từ Keyword Planner"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        # Parse JSON inputs
        import json
        keywords_list = json.loads(keywords)
        location_ids_list = json.loads(location_ids) if location_ids else None

        await ctx.info(f"💡 Lấy keyword ideas cho: {keywords_list}")

        result = await ads_client.get_keyword_ideas(
            access_token, customer_id, keywords_list, language_id, location_ids_list
        )

        ideas_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {ideas_count} keyword ideas")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy keyword ideas: {str(e)}")
        return f"❌ Lỗi lấy keyword ideas: {str(e)}"

@mcp.tool(description="Lấy danh sách bidding strategies")
async def get_bidding_strategies_tool(
    ctx,
    customer_id: str
) -> str:
    """Lấy danh sách bidding strategies"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🎯 Lấy bidding strategies cho customer: {customer_id}")

        result = await ads_client.get_bidding_strategies(access_token, customer_id)

        strategies_count = result.get('total_count', 0)
        await ctx.info(f"✅ Tìm thấy {strategies_count} bidding strategies")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi lấy bidding strategies: {str(e)}")
        return f"❌ Lỗi lấy bidding strategies: {str(e)}"

@mcp.tool(description="Tạo Target CPA bidding strategy")
async def create_target_cpa_bidding_strategy_tool(
    ctx,
    customer_id: str,
    strategy_name: str,
    target_cpa_micros: str
) -> str:
    """Tạo Target CPA bidding strategy"""
    try:
        access_token = extract_bearer_token_from_request()
        if not access_token:
            await ctx.error("❌ Không tìm thấy Bearer token trong headers")
            return "❌ Lỗi: Cần cung cấp Google OAuth2 access token trong Authorization header"

        await ctx.info(f"🎯 Tạo Target CPA strategy: {strategy_name}")
        await ctx.info(f"💰 Target CPA: {target_cpa_micros} micros")

        result = await ads_client.create_target_cpa_bidding_strategy(
            access_token, customer_id, strategy_name, target_cpa_micros
        )

        await ctx.info(f"✅ Tạo bidding strategy thành công: {result.get('strategy_resource_name')}")

        return json.dumps(result, ensure_ascii=False, indent=2)
    except Exception as e:
        await ctx.error(f"❌ Lỗi tạo bidding strategy: {str(e)}")
        return f"❌ Lỗi tạo bidding strategy: {str(e)}"

# Health check endpoint
@mcp.tool(description="Health check cho Google Ads server")
async def health_check() -> str:
    """Health check endpoint"""
    return json.dumps({
        "status": "healthy",
        "service": "Google Ads MCP Server",
        "port": HTTP_PORT
    })

if __name__ == "__main__":
    print("🚀 Starting Google Ads MCP Server")
    print("=" * 50)

    if TRANSPORT_TYPE == "sse":
        print(f"📡 Transport: SSE")
        print(f"🌐 SSE endpoint: http://{SSE_HOST}:{SSE_PORT}{SSE_PATH}")
        print(f"🏥 Health check: http://{SSE_HOST}:{SSE_PORT}/health")
        print("⚠️  Note: SSE transport fallback - HTTP transport preferred")

        # Sử dụng SSE transport
        try:
            mcp.run(host=SSE_HOST, port=SSE_PORT, path=SSE_PATH, transport="sse")
        except Exception as e:
            print(f"❌ SSE transport failed: {e}")
            print("🔄 Falling back to HTTP transport...")
            TRANSPORT_TYPE = "http"

    if TRANSPORT_TYPE == "http":
        print(f"📡 Transport: HTTP")
        print(f"🌐 HTTP endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print(f"🏥 Health check: http://{HTTP_HOST}:{HTTP_PORT}/health")

        # Sử dụng HTTP transport (mặc định)
        mcp.run(host=HTTP_HOST, port=HTTP_PORT, path=HTTP_PATH)
