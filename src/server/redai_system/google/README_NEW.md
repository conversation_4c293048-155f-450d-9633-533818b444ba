# Google MCP Servers

Bộ MCP Servers cho các dịch vụ Google APIs với Docker support.

## Servers

1. **Google Sheets Server** (Port: 8015)
   - Quản lý Google Spreadsheets
   - CRUD operations cho sheets và cells
   - Batch operations và formatting

2. **Google Docs Server** (Port: 8016)
   - Quản lý Google Documents
   - Text manipulation và formatting
   - Insert images, tables, page breaks

3. **Google Drive Server** (Port: 8017)
   - Quản lý files và folders trong Google Drive
   - Upload, download, search files
   - File sharing và permissions

4. **Google Gmail Server** (Port: 8018)
   - Quản lý Gmail emails
   - Send, read, search emails
   - Labels và draft management

5. **Google Calendar Server** (Port: 8019)
   - Quản lý Google Calendar
   - Events CRUD operations
   - Calendar management và freebusy queries

6. **Google Ads Server** (Port: 8020)
   - Quản lý Google Ads campaigns
   - Ad groups và keywords management
   - Performance reports và analytics

7. **Google Analytics Server** (Port: 8021)
   - Truy vấn dữ liệu Google Analytics 4
   - Realtime reports và historical data
   - Traffic sources và popular pages analysis

## Docker Deployment

### Chạy tất cả Google servers với Docker Compose:

```bash
# Build và start tất cả Google servers
docker-compose up -d google-sheets google-docs google-drive google-gmail google-calendar google-ads google-analytics

# Hoặc chạy từng server riêng lẻ
docker-compose up -d google-sheets
docker-compose up -d google-docs
docker-compose up -d google-drive
docker-compose up -d google-gmail
docker-compose up -d google-calendar
```

### Kiểm tra logs:

```bash
# Xem logs của tất cả Google servers
docker-compose logs -f google-sheets google-docs google-drive google-gmail google-calendar

# Xem logs của từng server
docker-compose logs -f google-sheets
docker-compose logs -f google-gmail
```

### Health checks:

```bash
# Kiểm tra health của servers
curl http://localhost:8015/health  # Google Sheets
curl http://localhost:8016/health  # Google Docs
curl http://localhost:8017/health  # Google Drive
curl http://localhost:8018/health  # Google Gmail
curl http://localhost:8019/health  # Google Calendar
curl http://localhost:8020/health  # Google Ads
curl http://localhost:8021/health  # Google Analytics
```

## Authentication

Tất cả servers sử dụng Google OAuth2 access tokens thông qua Authorization header:

```
Authorization: Bearer <your_google_oauth2_token>
```

## Port Mapping

| Server | Development Port | Docker Port | Container Name |
|--------|------------------|-------------|----------------|
| Google Sheets | 8015 | 8015 | google-sheets-server |
| Google Docs | 8016 | 8016 | google-docs-server |
| Google Drive | 8017 | 8017 | google-drive-server |
| Google Gmail | 8018 | 8018 | google-gmail-server |
| Google Calendar | 8019 | 8019 | google-calendar-server |
| Google Ads | 8020 | 8020 (HTTP), 8120 (SSE) | google-ads-server |
| Google Analytics | 8021 | 8021 | google-analytics-server |

## Environment Variables

```bash
# Google Sheets
GOOGLE_SHEETS_HTTP_HOST=0.0.0.0
GOOGLE_SHEETS_HTTP_PORT=8015
GOOGLE_SHEETS_HTTP_PATH=/mcp

# Google Docs
GOOGLE_DOCS_HTTP_HOST=0.0.0.0
GOOGLE_DOCS_HTTP_PORT=8016
GOOGLE_DOCS_HTTP_PATH=/mcp

# Google Drive
GOOGLE_DRIVE_HTTP_HOST=0.0.0.0
GOOGLE_DRIVE_HTTP_PORT=8017
GOOGLE_DRIVE_HTTP_PATH=/mcp

# Google Gmail
GOOGLE_GMAIL_HTTP_HOST=0.0.0.0
GOOGLE_GMAIL_HTTP_PORT=8018
GOOGLE_GMAIL_HTTP_PATH=/mcp

# Google Calendar
GOOGLE_CALENDAR_HTTP_HOST=0.0.0.0
GOOGLE_CALENDAR_HTTP_PORT=8019
GOOGLE_CALENDAR_HTTP_PATH=/mcp

# Google Ads
GOOGLE_ADS_TRANSPORT=http
GOOGLE_ADS_HTTP_HOST=0.0.0.0
GOOGLE_ADS_HTTP_PORT=8020
GOOGLE_ADS_HTTP_PATH=/mcp
GOOGLE_ADS_SSE_HOST=0.0.0.0
GOOGLE_ADS_SSE_PORT=8120
GOOGLE_ADS_SSE_PATH=/sse

# Google Ads API Credentials
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token_here
GOOGLE_ADS_LOGIN_CUSTOMER_ID=your_login_customer_id_here

# OAuth2 Credentials (optional)
GOOGLE_OAUTH2_CLIENT_ID=your_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_client_secret_here
GOOGLE_OAUTH2_REDIRECT_URI=http://localhost:8080/oauth2callback

# Google Analytics
GOOGLE_ANALYTICS_HTTP_HOST=0.0.0.0
GOOGLE_ANALYTICS_HTTP_PORT=8021
GOOGLE_ANALYTICS_HTTP_PATH=/mcp
```

## MCP Client Configuration

### Claude Desktop (Development):

```json
{
  "mcpServers": {
    "google-sheets": {
      "url": "http://127.0.0.1:8015/mcp"
    },
    "google-docs": {
      "url": "http://127.0.0.1:8016/mcp"
    },
    "google-drive": {
      "url": "http://127.0.0.1:8017/mcp"
    },
    "google-gmail": {
      "url": "http://127.0.0.1:8018/mcp"
    },
    "google-calendar": {
      "url": "http://127.0.0.1:8019/mcp"
    },
    "google-ads": {
      "url": "http://127.0.0.1:8020/mcp"
    },
    "google-analytics": {
      "url": "http://127.0.0.1:8021/mcp"
    }
  }
}
```

### Claude Desktop (Docker):

```json
{
  "mcpServers": {
    "google-sheets": {
      "url": "http://localhost:8015/mcp"
    },
    "google-docs": {
      "url": "http://localhost:8016/mcp"
    },
    "google-drive": {
      "url": "http://localhost:8017/mcp"
    },
    "google-gmail": {
      "url": "http://localhost:8018/mcp"
    },
    "google-calendar": {
      "url": "http://localhost:8019/mcp"
    }
  }
}
```

## Troubleshooting

### Common Issues:

1. **Port conflicts**: Đảm bảo ports 8015-8019 không bị sử dụng bởi services khác
2. **Google OAuth**: Cần valid Google OAuth2 access token với appropriate scopes
3. **Network issues**: Kiểm tra Docker network `redai-network` đã được tạo

### Debug Commands:

```bash
# Kiểm tra container status
docker-compose ps

# Restart specific service
docker-compose restart google-gmail

# Rebuild và restart
docker-compose up -d --build google-sheets

# View detailed logs
docker-compose logs --tail=100 google-drive
```
