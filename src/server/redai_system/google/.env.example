# Google Ads API Configuration
# Get these from Google Ads API Center: https://ads.google.com/nav/selectaccount?authuser=0&dst=/aw/apicenter

# Developer Token (required for Google Ads API)
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token_here

# Login Customer ID (Manager Account ID, optional)
GOOGLE_ADS_LOGIN_CUSTOMER_ID=your_manager_account_id_here

# Google Analytics Configuration
# No additional environment variables needed - uses OAuth2 access token from headers

# Server Configuration
GOOGLE_ADS_HTTP_HOST=0.0.0.0
GOOGLE_ADS_HTTP_PORT=8020
GOOGLE_ADS_HTTP_PATH=/mcp

GOOGLE_ANALYTICS_HTTP_HOST=0.0.0.0
GOOGLE_ANALYTICS_HTTP_PORT=8021
GOOGLE_ANALYTICS_HTTP_PATH=/mcp

# Logging
LOG_LEVEL=INFO
