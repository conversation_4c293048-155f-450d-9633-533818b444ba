# Google Ads API Configuration
# Get these from Google Ads API Center: https://ads.google.com/nav/selectaccount?authuser=0&dst=/aw/apicenter

# Developer Token (required for Google Ads API)
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token_here

# Login Customer ID (Manager Account ID, optional)
GOOGLE_ADS_LOGIN_CUSTOMER_ID=your_manager_account_id_here

# OAuth2 Configuration (for refresh token flow if needed)
# Get these from Google Cloud Console: https://console.cloud.google.com/apis/credentials
GOOGLE_OAUTH2_CLIENT_ID=your_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_client_secret_here
GOOGLE_OAUTH2_REDIRECT_URI=http://localhost:8080/oauth2callback

# Google Analytics Configuration
# No additional environment variables needed - uses OAuth2 access token from headers

# Server Configuration
# Transport type: "http" (preferred) hoặc "sse" (fallback)
GOOGLE_ADS_TRANSPORT=http

# HTTP Transport Configuration
GOOGLE_ADS_HTTP_HOST=0.0.0.0
GOOGLE_ADS_HTTP_PORT=8020
GOOGLE_ADS_HTTP_PATH=/mcp

# SSE Transport Configuration (fallback)
GOOGLE_ADS_SSE_HOST=0.0.0.0
GOOGLE_ADS_SSE_PORT=8120
GOOGLE_ADS_SSE_PATH=/sse

GOOGLE_ANALYTICS_HTTP_HOST=0.0.0.0
GOOGLE_ANALYTICS_HTTP_PORT=8021
GOOGLE_ANALYTICS_HTTP_PATH=/mcp

# Logging
LOG_LEVEL=INFO
