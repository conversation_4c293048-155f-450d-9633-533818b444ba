"""
Server MCP cho RedAI Model Module API sử dụng FastMCP và OpenAPI Schema

Server này tự động tạo các MCP tools và resources từ model/swagger.json,
cung cấp giao diện MCP cho tất cả các endpoint của Model Module API.

Tính năng:
- Tự động tạo tools từ OpenAPI specification
- Hỗ trợ Bearer token authentication từ client
- Streamable HTTP transport với FastMCP
- Tùy chỉnh route mapping cho các loại endpoint khác nhau
- Xử lý parameters, headers và request body tự động

Cấu trúc API:
- Key LLM endpoints: Quản lý API keys LLM với model auto-discovery
- Models endpoints: Quản lý user models, system models, fine-tune models
- Data Fine Tune endpoints: Quản lý datasets fine-tuning với upload
- Fine Tuning Jobs endpoints: Tạo và quản lý fine-tuning jobs

Authentication:
- Bearer token được truyền từ client khi kết nối
- Tự động xử lý authentication cho tất cả requests
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional, Dict, Any

# FastMCP imports
from fastmcp import FastMCP, Context
from fastmcp.server.dependencies import get_http_request

# Cấu hình môi trường
API_BASE_URL = os.getenv("REDAI_MODEL_API_BASE_URL", "https://api.redai.com")
HTTP_HOST = os.getenv("MODEL_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("MODEL_HTTP_PORT", "8006"))
HTTP_PATH = os.getenv("MODEL_HTTP_PATH", "/mcp")

# Token extraction - không cần context storage

def extract_bearer_token_from_request() -> Optional[str]:
    """
    Extract Bearer token từ HTTP request headers

    Cách này tối ưu vì:
    - Token được extract fresh mỗi lần gọi
    - Không có caching issues
    - Tự động cleanup khi request kết thúc
    - Không cần quản lý context lifecycle
    """
    try:
        request = get_http_request()
        if request:
            # Thử cả "Authorization" và "authorization" (case-insensitive)
            auth_header = request.headers.get("Authorization") or request.headers.get("authorization", "")
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
    except Exception:
        pass
    return None

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file swagger.json trong cùng thư mục
    """
    schema_path = Path(__file__).parent / "swagger.json"
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")

    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, bearer_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với Bearer token authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if bearer_token:
        headers["Authorization"] = f"Bearer {bearer_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

class ModelServer:
    """
    Class để quản lý Model MCP Server với authentication động
    """
    
    def __init__(self):
        self.openapi_spec = load_openapi_schema()
        self.base_url = API_BASE_URL
        self._mcp_server = None
        
    def create_server(self) -> FastMCP:
        """
        Tạo MCP server với custom token extraction từ headers
        """
        # Tạo MCP server không có built-in authentication
        mcp = FastMCP(name="RedAI-Model-Server")

        # Tạo tools từ OpenAPI spec với token support
        self._add_openapi_tools(mcp)

        return mcp

    def _add_openapi_tools(self, mcp_server: FastMCP) -> None:
        """
        Thêm tools từ OpenAPI spec với Bearer token từ context
        """
        paths = self.openapi_spec.get("paths", {})

        for path, methods in paths.items():
            for method, operation in methods.items():
                if method.lower() in ["get", "post", "put", "delete", "patch"]:
                    self._create_api_tool(mcp_server, path, method.upper(), operation)

    def _create_api_tool(self, mcp_server: FastMCP, path: str, method: str, operation: Dict[str, Any]) -> None:
        """
        Tạo một tool cho API endpoint cụ thể với parameters được định nghĩa từ OpenAPI spec
        """
        operation_id = operation.get("operationId", f"{method.lower()}_{path.replace('/', '_').replace('{', '').replace('}', '')}")
        summary = operation.get("summary", f"{method} {path}")
        description = operation.get("description", summary)

        # Tạo function với tên unique
        def create_api_function():
            async def api_function(ctx: Context) -> str:
                f"""
                {description}

                Endpoint: {method} {path}

                Gọi API endpoint này với authentication tự động từ Bearer token.
                """
                try:
                    # Extract Bearer token từ request headers mỗi lần gọi tool
                    token = extract_bearer_token_from_request()

                    # Debug: Log chi tiết về token
                    await ctx.info(f"🔍 Debug - Token extraction result: {token[:30] if token else 'None'}...")

                    # Tạo HTTP client với Bearer token nếu có
                    headers = {
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }

                    if token:
                        headers["Authorization"] = f"Bearer {token}"
                        await ctx.info(f"🔑 Sử dụng Bearer token cho API call: {token[:20]}...")
                        await ctx.info(f"📋 Request headers: {list(headers.keys())}")
                    else:
                        await ctx.info(f"⚠️ Không có Bearer token - gọi API không có authentication")
                        await ctx.info(f"📋 Request headers: {list(headers.keys())}")

                    async with httpx.AsyncClient(
                        base_url=self.base_url,
                        headers=headers,
                        timeout=30.0
                    ) as client:

                        await ctx.info(f"🌐 Gọi API: {method} {self.base_url}{path}")
                        await ctx.info(f"📤 Request URL: {self.base_url}{path}")
                        await ctx.info(f"📤 Request Method: {method}")

                        # Thực hiện API call với Bearer token nếu có
                        response = await client.request(
                            method=method,
                            url=path
                        )

                        response.raise_for_status()

                        # Trả về response
                        if response.headers.get("content-type", "").startswith("application/json"):
                            result = response.json()
                            await ctx.info(f"✅ API call thành công")
                            return json.dumps(result, ensure_ascii=False, indent=2)
                        else:
                            await ctx.info(f"✅ API call thành công")
                            return response.text

                except Exception as e:
                    await ctx.error(f"❌ Lỗi khi gọi API: {str(e)}")

                    # Debug thêm thông tin về lỗi
                    if "401" in str(e):
                        await ctx.error(f"🔐 401 Unauthorized - Kiểm tra Bearer token:")
                        await ctx.error(f"   • Token có được gửi từ client không?")
                        await ctx.error(f"   • Token có đúng format không?")
                        await ctx.error(f"   • Token có hết hạn không?")
                        await ctx.error(f"   • API endpoint có yêu cầu authentication khác không?")

                    return f"❌ Lỗi API call: {str(e)}\n\n" \
                           f"💡 Nếu lỗi 401: Kiểm tra Bearer token trong Authorization header khi connect"

            # Đặt tên và docstring cho function
            api_function.__name__ = operation_id
            api_function.__doc__ = f"{description}\n\nEndpoint: {method} {path}\n\nGọi API endpoint này với Bearer token tự động từ context nếu có."

            return api_function

        # Tạo và đăng ký tool
        tool_func = create_api_function()
        mcp_server.tool()(tool_func)

# Tạo instance của ModelServer
model_server = ModelServer()

# Tạo MCP server với Bearer token authentication
mcp = model_server.create_server()

def run_server_with_transport(transport: str = "streamable-http"):
    """
    Chạy server với transport cụ thể theo tài liệu FastMCP

    Args:
        transport: Loại transport ("streamable-http", "sse", "stdio")
    """
    try:
        if transport == "streamable-http" or transport == "http":
            print(f"🚀 Khởi động server với Streamable HTTP transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            # Sử dụng FastMCP với streamable-http transport theo tài liệu
            mcp.run(
                transport="streamable-http",
                host=HTTP_HOST,
                port=HTTP_PORT,
                path="/mcp"
            )

        elif transport == "sse":
            print(f"🚀 Khởi động server với SSE transport tại http://{HTTP_HOST}:{HTTP_PORT}")
            mcp.run(
                transport="sse",
                host=HTTP_HOST,
                port=HTTP_PORT
            )
        elif transport == "stdio":
            print("🚀 Khởi động server với STDIO transport")
            mcp.run(transport="stdio")
        else:
            raise ValueError(f"Unsupported transport: {transport}")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server với {transport}: {str(e)}")
        raise

def print_server_info():
    """
    In thông tin về server và các endpoints có sẵn
    """
    try:
        schema = model_server.openapi_spec
        endpoints = list(schema.get("paths", {}).keys())

        print("="*70)
        print("🤖 RedAI Model MCP Server")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 Authentication: Bearer Token từ client (Authorization header)")
        print(f"   🚀 Transport: Streamable HTTP (FastMCP)")
        print(f"   🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"   📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}/mcp")

        print(f"   🔐 Authentication: Custom Bearer token extraction từ headers")
        print(f"   💡 Token được extract từ Authorization header và tự động sử dụng cho API calls")
        print()

        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()

        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            # Lấy tags từ operation đầu tiên
            first_method = list(schema["paths"][path].values())[0]
            tags = first_method.get("tags", [])
            tag_str = f" [{', '.join(tags)}]" if tags else ""
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]{tag_str}")

        print()
        print("🔧 Available Tools:")
        print("   • check_auth_status: Kiểm tra Bearer token và authentication status")
        print("   • debug_request_headers: Debug HTTP request headers")
        print("   • get_model_summary: Lấy tổng quan models")
        print("   • calculate_fine_tune_cost: Tính toán chi phí fine-tuning")
        print("   • suggest_model_for_task: Gợi ý model cho task")
        print("   • validate_api_key_format: Kiểm tra format API key")
        print("   • [API Tools]: Các tools được tạo tự động từ OpenAPI spec với Bearer token support")

        print()
        print("🚀 Transport Options:")
        print("   • Streamable HTTP: http://127.0.0.1:8006/mcp (mặc định)")
        print("   • SSE: http://127.0.0.1:8006/sse")
        print("   • STDIO: Command line interface")
        print("   💡 Sử dụng: python model_server.py [streamable-http|sse|stdio]")

        print("="*70)

    except Exception as e:
        print(f"❌ Lỗi khi tải thông tin server: {str(e)}")

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')

        # In thông tin server
        print_server_info()
        print("🚀 Đang khởi động server...")
        print("💡 Client cần gửi Bearer token trong Authorization header khi connect")
        print("💡 Token sẽ được tự động sử dụng cho tất cả API calls")
        print()

        # Lấy transport từ environment variable hoặc command line args
        preferred_transport = os.getenv("MODEL_TRANSPORT", "streamable-http")

        # Kiểm tra command line arguments
        import sys
        if len(sys.argv) > 1:
            if sys.argv[1] in ["streamable-http", "http", "sse", "stdio"]:
                preferred_transport = sys.argv[1]

        print(f"🔄 Sử dụng {preferred_transport} transport...")

        # Thử khởi động với transport được chọn
        try:
            run_server_with_transport(preferred_transport)
        except Exception as e:
            print(f"❌ Lỗi với {preferred_transport} transport: {str(e)}")

            # Fallback sang streamable-http nếu không phải streamable-http
            if preferred_transport != "streamable-http":
                print("🔄 Thử fallback sang streamable-http transport...")
                try:
                    run_server_with_transport("streamable-http")
                except Exception as e2:
                    print(f"❌ Lỗi với streamable-http transport: {str(e2)}")
                    raise e2
            else:
                raise e

    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
