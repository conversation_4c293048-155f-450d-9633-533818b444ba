# RedAI Marketing Module MCP Server

Server MCP cho RedAI Marketing Module API sử dụng FastMCP và OpenAPI Schema.

## Tính năng

- **Audience Management**: Quản lý audience và khách hàng mục tiêu
- **Campaign Management**: <PERSON><PERSON><PERSON><PERSON> lý chiến dịch marketing đa kênh
- **Email Marketing**: Quản lý email campaigns và templates
- **Zalo Integration**: <PERSON><PERSON><PERSON> hợ<PERSON>alo OA, ZNS và automation
- **Segmentation & Tagging**: Phân khúc khách hàng và gắn thẻ
- **Marketing Analytics**: Thống kê và báo cáo marketing
- **Custom Fields**: Quản lý trường tùy chỉnh cho marketing
- **Admin Management**: Quản lý cấp admin

## Chạy với Docker

### 1. Build và chạy với Docker

```bash
# Build image
docker build -t redai-marketing-server .

# Chạy container
docker run -d \
  --name redai-marketing-server \
  -p 8011:8011 \
  -e REDAI_MARKETING_API_BASE_URL=https://api.redai.com \
  redai-marketing-server
```

### 2. Sử dụng Docker Compose (Khuyến nghị)

```bash
# Chạy tất cả services (từ root directory)
docker-compose up -d

# Chỉ chạy marketing service
docker-compose up -d redai-marketing

# Xem logs
docker-compose logs -f redai-marketing

# Dừng services
docker-compose down
```

## Cấu hình Environment Variables

| Variable | Mặc định | Mô tả |
|----------|----------|-------|
| `REDAI_MARKETING_API_BASE_URL` | `https://api.redai.com` | Base URL của Marketing API |
| `MARKETING_HTTP_HOST` | `0.0.0.0` | Host để bind server |
| `MARKETING_HTTP_PORT` | `8011` | Port của MCP server |
| `MARKETING_HTTP_PATH` | `/mcp` | Path endpoint cho MCP |
| `MARKETING_TRANSPORT` | `streamable-http` | Transport method |

## Endpoints

- **MCP Server**: `http://localhost:8011/mcp`
- **Health Check**: `http://localhost:8011/health`

## Chạy Development

```bash
# Cài đặt dependencies
pip install -r requirements.txt

# Chạy server
python marketing_server.py

# Hoặc với transport cụ thể
python marketing_server.py streamable-http
```

## API Tools

Server tự động tạo tools từ `swagger.json` và cung cấp các tools đặc biệt:

- `get_marketing_summary`: Lấy tổng quan Marketing Module
- `get_marketing_endpoints_by_category`: Lọc endpoints theo danh mục
- `get_zalo_campaign_analytics`: Analytics cho Zalo campaigns

## Authentication

Server sử dụng Bearer Token authentication:
- Token được truyền từ client trong Authorization header
- Tự động sử dụng token cho tất cả API calls

## Logs

Logs được lưu trong thư mục `/app/logs` trong container và có thể mount ra host:

```bash
docker run -v ./logs:/app/logs redai-marketing-server
```

## API Categories

### User Endpoints
- **Audience**: Quản lý audience và khách hàng mục tiêu
- **Campaign**: Quản lý chiến dịch marketing
- **Email Campaign**: Quản lý email marketing
- **Template Email**: Quản lý templates email
- **Segment**: Phân khúc khách hàng
- **Tag**: Gắn thẻ và phân loại
- **Marketing Custom Field**: Trường tùy chỉnh
- **Marketing Statistics**: Thống kê và analytics
- **Zalo Integration**: Tích hợp Zalo đầy đủ

### Admin Endpoints
- **Admin Audience**: Quản lý audience cấp admin
- **Admin Segment**: Quản lý segment cấp admin
- **Admin Tag**: Quản lý tag cấp admin
- **Admin Template Email**: Quản lý email template cấp admin
- **Admin Marketing Custom Field**: Quản lý custom field cấp admin
