{"openapi": "3.0.0", "info": {"title": "Data Module API", "description": "API documentation for Data Module - <PERSON><PERSON><PERSON><PERSON> lý dữ liệu trong hệ thống bao gồm Media, URL, Knowledge Files và Statistics", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "Admin Media", "description": "Quản lý media cho admin"}, {"name": "User Media", "description": "Quản lý media cho người dùng"}, {"name": "Admin URL", "description": "<PERSON><PERSON><PERSON>n lý URL cho admin"}, {"name": "User URL", "description": "Q<PERSON>ản lý URL cho người dùng"}, {"name": "Admin Knowledge Files", "description": "<PERSON><PERSON><PERSON><PERSON> lý file tri thức cho admin"}, {"name": "User Knowledge Files", "description": "<PERSON><PERSON><PERSON><PERSON> lý file tri thức cho người dùng"}, {"name": "Admin Vector Store", "description": "Quản lý vector store cho admin"}, {"name": "User Vector Store", "description": "Quản lý vector store cho người dùng"}, {"name": "Admin Statistics", "description": "<PERSON><PERSON><PERSON><PERSON> kê dữ liệu cho admin"}, {"name": "User Statistics", "description": "<PERSON>h<PERSON><PERSON> kê dữ liệu cho người dùng"}], "paths": {"/media/my-media": {"get": {"operationId": "redai-data-getMyMedia", "tags": ["User Media"], "summary": "<PERSON><PERSON><PERSON> danh sách media của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> danh sách media thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọc và phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "search", "in": "query", "description": "Từ khóa tìm kiếm trong tên hoặc mô tả media", "required": false, "schema": {"type": "string", "example": "beautiful image"}}, {"name": "status", "in": "query", "description": "Trạng thái c<PERSON>a media", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "APPROVED", "PENDING", "REJECTED"], "example": "APPROVED"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách media thành công."}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MediaDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"operationId": "redai-data-deleteMyMediaBatch", "tags": ["User Media"], "summary": "<PERSON>óa mềm nhiều media cho người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> mềm nhiều media thuộc sở hữu của người dùng hiện tại", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteMediaDto"}, "examples": {"single": {"summary": "Xóa một media", "value": {"mediaIds": ["123e4567-e89b-12d3-a456-************"]}}, "multiple": {"summary": "Xóa nhiều media", "value": {"mediaIds": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"]}}}}}}, "responses": {"200": {"description": "Xóa media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Xóa media thành công."}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data/url": {"get": {"operationId": "redai-data-getMyUrls", "tags": ["User URL"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch URL của người dùng", "description": "<PERSON><PERSON><PERSON> danh sách URL thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọc theo tags, type và phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "keyword", "in": "query", "description": "Từ khóa tìm kiếm URL, tìm kiếm theo title, content và url", "required": false, "schema": {"type": "string", "example": "google"}}, {"name": "type", "in": "query", "description": "Loại URL cần lọc", "required": false, "schema": {"type": "string", "example": "web"}}, {"name": "tags", "in": "query", "description": "<PERSON><PERSON><PERSON> thẻ cần lọc", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "example": ["<PERSON><PERSON><PERSON>", "tutorial"]}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["title", "createdAt", "updatedAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách URL thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UrlSchema"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"operationId": "redai-data-createNewUrl", "tags": ["User URL"], "summary": "Tạo URL mới", "description": "Tạo một URL mới cho người dùng hiện tại", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUrlDto"}, "examples": {"basic": {"summary": "URL c<PERSON> bản", "value": {"url": "https://example.com/article", "title": "<PERSON><PERSON><PERSON> viết hay", "content": "<PERSON><PERSON>i dung bài viết...", "type": "web", "tags": ["article", "tutorial"]}}, "minimal": {"summary": "URL tối thiểu", "value": {"url": "https://google.com", "title": "Google", "content": "Trang chủ Google"}}}}}}, "responses": {"201": {"description": "Tạo URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "URL đã đư<PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/UrlSchema"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data/url/{id}": {"get": {"operationId": "redai-data-getUrlDetails", "tags": ["User URL"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết URL", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một URL cụ thể theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của URL", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin URL thành công"}, "data": {"$ref": "#/components/schemas/UrlSchema"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"operationId": "redai-data-updateUrl", "tags": ["User URL"], "summary": "<PERSON><PERSON><PERSON> nhật thông tin URL", "description": "Cập nhật thông tin của một URL cụ thể. Chỉ có thể cập nhật URL thuộc sở hữu của người dùng hiện tại.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của URL", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUrlDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t URL thành công"}, "data": {"$ref": "#/components/schemas/UrlSchema"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"operationId": "redai-data-deleteUrl", "tags": ["User URL"], "summary": "Xóa URL", "description": "Xóa một URL cụ thể. Chỉ có thể xóa URL thuộc sở hữu của người dùng hiện tại.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của URL", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Xóa URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Xóa URL thành công"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/statistics": {"get": {"operationId": "redai-data-getUserStatistics", "tags": ["User Statistics"], "summary": "<PERSON><PERSON><PERSON> thống kê dữ liệu của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> thống kê về số lượng media, knowledge files, URLs và vector stores của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê thành công"}, "data": {"$ref": "#/components/schemas/UserStatisticsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/statistics/storage": {"get": {"operationId": "redai-data-getStorageStatistics", "tags": ["User Statistics"], "summary": "<PERSON><PERSON><PERSON> thống kê dung lượng của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> thống kê chi tiết về dung lượng lưu trữ của người dùng hiện tại theo từng loại dữ liệu", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê dung lượng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê dung lượng thành công"}, "data": {"$ref": "#/components/schemas/UserStorageStatisticsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"AdminMediaResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của media", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên file media", "example": "My beautiful image"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về media", "example": "An image uploaded by user", "nullable": true}, "size": {"type": "integer", "description": "Dung lượng file (byte)", "example": 1048576}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Mảng tag phân loại media", "example": ["shoe", "white sneaker"]}, "type": {"type": "string", "description": "Type phân loại media", "example": "image/png"}, "storageKey": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> lưu trữ trên S3", "example": "test/test-1744857340088-abc123"}, "ownedBy": {"type": "integer", "description": "ID người sở hữu", "example": 1}, "ownerType": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Loại chủ sở hữu", "example": "USER"}, "status": {"type": "string", "enum": ["DRAFT", "APPROVED", "PENDING", "REJECTED"], "description": "Trạng thái c<PERSON>a media", "example": "APPROVED"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON> nh<PERSON> (timestamp)", "example": 1640995200000}, "viewUrl": {"type": "string", "description": "URL xem media", "example": "https://example.com/media/123e4567-e89b-12d3-a456-************.jpg"}}, "required": ["id", "name", "size", "tags", "type", "storageKey", "ownedBy", "ownerType", "status", "createdAt", "updatedAt", "viewUrl"]}, "MediaResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của media", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên file media", "example": "My beautiful image"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về media", "example": "An image uploaded by user", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Mảng tag phân loại media", "example": ["shoe", "white sneaker"]}, "downloadUrl": {"type": "string", "description": "URL tải xuống media", "example": "https://example.com/media/123e4567-e89b-12d3-a456-************.jpg"}, "size": {"type": "integer", "description": "Dung lượng file (byte)", "example": 1048576}, "type": {"type": "string", "description": "Type phân loại media", "example": "image/png"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}}, "required": ["id", "name", "tags", "downloadUrl", "size", "type", "createdAt"]}, "MediaDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên file media", "example": "My beautiful image"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về media", "example": "An image uploaded by user", "nullable": true}, "size": {"type": "integer", "description": "Dung lượng file (byte)", "example": 1048576, "minimum": 1}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Mảng tag phân loại media", "example": ["shoe", "white sneaker"]}, "type": {"type": "string", "description": "Type phân loại media (chỉ hỗ trợ image/, video/, audio/)", "example": "image/png"}, "viewUrl": {"type": "string", "description": "URL xem media", "example": "test/test-1744857340088-abc123"}}, "required": ["name", "size", "tags", "type", "viewUrl"]}, "DeleteMediaDto": {"type": "object", "properties": {"mediaIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Danh sách ID của media cần xóa", "example": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"], "minItems": 1}}, "required": ["mediaIds"]}, "UrlSchema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của URL", "example": "123e4567-e89b-12d3-a456-************"}, "url": {"type": "string", "format": "uri", "description": "Đường dẫn URL", "example": "https://example.com/article/how-to-use-nestjs"}, "title": {"type": "string", "description": "Tiêu đề của tài nguyên URL", "example": "Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao"}, "content": {"type": "string", "description": "Nội dung về tài nguyên URL", "example": "<PERSON><PERSON><PERSON> viết này hướng dẫn cách sử dụng NestJS..."}, "type": {"type": "string", "description": "<PERSON><PERSON>i tài nguyên URL", "example": "web", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Các thẻ phân loại URL", "example": ["<PERSON><PERSON><PERSON>", "tutorial", "backend"], "nullable": true}, "ownedBy": {"type": "integer", "description": "ID người sở hữu", "example": 1}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true}, "ownedByEnum": {"type": "string", "enum": ["ADMIN", "USER"], "description": "Loại người sở hữu URL", "example": "USER", "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON> nh<PERSON> (timestamp)", "example": 1640995200000}}, "required": ["id", "url", "title", "content", "ownedBy", "isActive", "createdAt", "updatedAt"]}, "CreateUrlDto": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "Đường dẫn URL", "example": "https://example.com/article/how-to-use-nestjs", "maxLength": 2048}, "title": {"type": "string", "description": "Tiêu đề của tài nguyên URL", "example": "Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao", "maxLength": 512}, "content": {"type": "string", "description": "Nội dung về tài nguyên URL", "example": "<PERSON><PERSON><PERSON> viết này hướng dẫn cách sử dụng NestJS..."}, "type": {"type": "string", "description": "<PERSON><PERSON>i tài nguyên URL", "example": "web", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Các thẻ phân loại URL", "example": ["<PERSON><PERSON><PERSON>", "tutorial", "backend"], "nullable": true}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true, "default": true}}, "required": ["url", "title", "content"]}, "UpdateUrlDto": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "Đường dẫn URL", "example": "https://example.com/article/how-to-use-nestjs-updated", "maxLength": 2048}, "title": {"type": "string", "description": "Tiêu đề của tài nguyên URL", "example": "Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao - <PERSON><PERSON><PERSON> nhật", "maxLength": 512}, "content": {"type": "string", "description": "Nội dung về tài nguyên URL", "example": "<PERSON><PERSON><PERSON> viết này hướng dẫn cách sử dụng NestJS... (đã cập nhật)"}, "type": {"type": "string", "description": "<PERSON><PERSON>i tài nguyên URL", "example": "web", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Các thẻ phân loại URL", "example": ["<PERSON><PERSON><PERSON>", "tutorial", "backend", "updated"], "nullable": true}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true}}}, "FileResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của file tri thức", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên file tri thức", "example": "Document.pdf"}, "storageKey": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> lưu trữ trên S3", "example": "knowledge-files/user-1/document.pdf"}, "ownerType": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Loại chủ sở hữu", "example": "USER"}, "ownedBy": {"type": "integer", "description": "ID người sở hữu", "example": 1}, "isOwner": {"type": "boolean", "description": "<PERSON><PERSON> phải chủ sở hữu không", "example": true}, "isForSale": {"type": "boolean", "description": "<PERSON><PERSON>ang bán kh<PERSON>ng", "example": false}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}, "storage": {"type": "integer", "description": "Dung lượng file (bytes)", "example": 1048576}, "fileId": {"type": "string", "description": "ID file trên OpenAI", "example": "file-abc123"}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DISABLED"], "description": "Tr<PERSON><PERSON> thái của file", "example": "APPROVED"}, "sourceId": {"type": "string", "format": "uuid", "description": "ID file gốc (nếu là bản sao)", "example": "456e7890-e89b-12d3-a456-************", "nullable": true}}, "required": ["id", "name", "storageKey", "ownerType", "ownedBy", "isOwner", "isForSale", "createdAt", "storage", "fileId", "status"]}, "BatchCreateFilesDto": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên file", "example": "Document.pdf"}, "storageKey": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>u trữ", "example": "knowledge-files/user-1/document.pdf"}, "fileId": {"type": "string", "description": "ID file trên OpenAI", "example": "file-abc123"}, "storage": {"type": "integer", "description": "Dung lượng file (bytes)", "example": 1048576}}, "required": ["name", "storageKey", "fileId", "storage"]}, "description": "<PERSON><PERSON> s<PERSON>ch file cần tạo", "minItems": 1}}, "required": ["files"]}, "BatchCreateFilesResponseDto": {"type": "object", "properties": {"createdFiles": {"type": "array", "items": {"$ref": "#/components/schemas/FileResponseDto"}, "description": "<PERSON><PERSON> sách file đã tạo thành công"}, "failedFiles": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "Document.pdf"}, "error": {"type": "string", "example": "File already exists"}}}, "description": "<PERSON><PERSON> s<PERSON>ch file tạo thất bại"}}, "required": ["createdFiles", "failedFiles"]}, "DeleteFilesDto": {"type": "object", "properties": {"fileIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Danh sách ID file cần xóa", "example": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"], "minItems": 1}}, "required": ["fileIds"]}, "VectorStoreResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của vector store", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên vector store", "example": "My Vector Store"}, "description": {"type": "string", "description": "Mô tả vector store", "example": "Vector store for documents", "nullable": true}, "vectorStoreId": {"type": "string", "description": "ID vector store trên OpenAI", "example": "vs_abc123"}, "ownerType": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Loại chủ sở hữu", "example": "USER"}, "ownedBy": {"type": "integer", "description": "ID người sở hữu", "example": 1}, "isOwner": {"type": "boolean", "description": "<PERSON><PERSON> phải chủ sở hữu không", "example": true}, "isForSale": {"type": "boolean", "description": "<PERSON><PERSON>ang bán kh<PERSON>ng", "example": false}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DISABLED"], "description": "Trạng thái của vector store", "example": "APPROVED"}, "sourceId": {"type": "string", "format": "uuid", "description": "ID vector store gốc (nếu là bản sao)", "example": "456e7890-e89b-12d3-a456-************", "nullable": true}}, "required": ["id", "name", "vectorStoreId", "ownerType", "ownedBy", "isOwner", "isForSale", "createdAt", "status"]}, "StatisticsResponseDto": {"type": "object", "properties": {"totalUsers": {"type": "integer", "description": "Tổng số người dùng trong hệ thống", "example": 1250}, "totalKnowledgeFiles": {"type": "integer", "description": "Tổng số file tri thức trong hệ thống", "example": 350}, "totalMedia": {"type": "integer", "description": "Tổng số media trong hệ thống", "example": 780}, "totalUrls": {"type": "integer", "description": "Tổng số URL trong hệ thống", "example": 420}, "totalVectorStores": {"type": "integer", "description": "Tổng số vector store trong hệ thống", "example": 85}}, "required": ["totalUsers", "totalKnowledgeFiles", "totalMedia", "totalUrls", "totalVectorStores"]}, "StorageBreakdownDto": {"type": "object", "properties": {"mediaSize": {"type": "integer", "description": "Dung lượng media files (bytes)", "example": 1073741824}, "knowledgeFileSize": {"type": "integer", "description": "Dung lượng knowledge files (bytes)", "example": 536870912}, "vectorStoreSize": {"type": "integer", "description": "Dung lượng vector stores (bytes)", "example": 268435456}}, "required": ["mediaSize", "knowledgeFileSize", "vectorStoreSize"]}, "StorageStatisticsResponseDto": {"type": "object", "properties": {"totalStorage": {"type": "integer", "description": "Tổng dung lượng lưu trữ (bytes)", "example": 1878048768}, "breakdown": {"$ref": "#/components/schemas/StorageBreakdownDto"}, "storageLimit": {"type": "integer", "description": "G<PERSON>ới hạn dung lượng (bytes)", "example": 10737418240}, "usagePercentage": {"type": "number", "format": "float", "description": "<PERSON>ần tr<PERSON>m sử dụng dung lượng", "example": 17.5}}, "required": ["totalStorage", "breakdown", "storageLimit", "usagePercentage"]}, "UserStatisticsResponseDto": {"type": "object", "properties": {"totalMedia": {"type": "integer", "description": "Tổng số media files của người dùng", "example": 25}, "totalKnowledgeFiles": {"type": "integer", "description": "Tổng số knowledge files của người dùng", "example": 12}, "totalUrls": {"type": "integer", "description": "Tổng số URLs của người dùng", "example": 8}, "totalVectorStores": {"type": "integer", "description": "Tổng số vector stores của người dùng", "example": 3}}, "required": ["totalMedia", "totalKnowledgeFiles", "totalUrls", "totalVectorStores"]}, "UserStorageBreakdownDto": {"type": "object", "properties": {"mediaSize": {"type": "integer", "description": "Dung lượng media files của người dùng (bytes)", "example": 52428800}, "knowledgeFileSize": {"type": "integer", "description": "Dung lượng knowledge files của người dùng (bytes)", "example": 26214400}, "vectorStoreSize": {"type": "integer", "description": "Dung lượng vector stores của người dùng (bytes)", "example": 13107200}}, "required": ["mediaSize", "knowledgeFileSize", "vectorStoreSize"]}, "UserStorageStatisticsResponseDto": {"type": "object", "properties": {"totalStorage": {"type": "integer", "description": "Tổng dung lượng lưu trữ của người dùng (bytes)", "example": 91750400}, "breakdown": {"$ref": "#/components/schemas/UserStorageBreakdownDto"}, "storageLimit": {"type": "integer", "description": "<PERSON><PERSON><PERSON>i hạn dung lượng của người dùng (bytes)", "example": 1073741824}, "usagePercentage": {"type": "number", "format": "float", "description": "<PERSON>ần tr<PERSON>m sử dụng dung lượng", "example": 8.5}}, "required": ["totalStorage", "breakdown", "storageLimit", "usagePercentage"]}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "limit": {"type": "integer", "description": "Số lượng item mỗi trang", "example": 10}, "total": {"type": "integer", "description": "Tổng số item", "example": 100}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}}, "required": ["page", "limit", "total", "totalPages"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}