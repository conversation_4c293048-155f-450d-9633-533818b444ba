{"openapi": "3.0.0", "info": {"title": "Data Module API", "description": "API documentation for Data Module - <PERSON><PERSON><PERSON><PERSON> lý dữ liệu trong hệ thống bao gồm Media, URL, Knowledge Files và Statistics", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User Media", "description": "Quản lý media cho người dùng"}, {"name": "User URL", "description": "Q<PERSON>ản lý URL cho người dùng"}, {"name": "User Knowledge Files", "description": "<PERSON><PERSON><PERSON><PERSON> lý file tri thức cho người dùng"}, {"name": "User Statistics", "description": "<PERSON>h<PERSON><PERSON> kê dữ liệu cho người dùng"}], "paths": {"/media/my-media": {"get": {"tags": ["User Media"], "summary": "<PERSON><PERSON><PERSON> danh sách media của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> danh sách media thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọc và phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "search", "in": "query", "description": "Từ khóa tìm kiếm trong tên hoặc mô tả media", "required": false, "schema": {"type": "string", "example": "beautiful image"}}, {"name": "status", "in": "query", "description": "Trạng thái c<PERSON>a media", "required": false, "schema": {"type": "string", "enum": ["DRAFT", "APPROVED", "PENDING", "REJECTED"], "example": "APPROVED"}}, {"name": "ownerType", "in": "query", "description": "Loại chủ sở hữu media", "required": false, "schema": {"type": "string", "enum": ["USER", "ADMIN"], "example": "USER"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "example": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "example": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách media thành công."}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MediaDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User Media"], "summary": "<PERSON>óa mềm nhiều media cho người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> mềm nhiều media thuộc sở hữu của người dùng hiện tại", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteMediaDto"}, "examples": {"single": {"summary": "Xóa một media", "value": {"mediaIds": ["123e4567-e89b-12d3-a456-************"]}}, "multiple": {"summary": "Xóa nhiều media", "value": {"mediaIds": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"]}}}}}}, "responses": {"200": {"description": "Xóa media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Xóa media thành công."}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/media/{id}": {"get": {"tags": ["User Media"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết media theo ID", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một media cụ thể theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của media (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin chi tiết media thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin chi tiết media thành công."}, "data": {"$ref": "#/components/schemas/MediaDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/media/presigned-urls": {"post": {"tags": ["User Media"], "summary": "Tạo presigned URLs cho danh sách media", "description": "Tạo presigned URLs để truy cập các media từ danh sách media được cung cấp", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MediaCreateDto"}}, "examples": {"basic": {"summary": "Tạo presigned URLs c<PERSON> bản", "value": [{"name": "My beautiful image", "description": "An image uploaded by user", "size": 1048576, "tags": ["image", "beautiful"], "type": "image/png"}, {"name": "Demo video", "description": "A demo video file", "size": 5242880, "tags": ["video", "demo"], "type": "video/mp4"}]}}}}}, "responses": {"201": {"description": "Tạo presigned URLs thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Tạo presigned URLs thành công."}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MediaPresignedUrlResponseDto"}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data/url": {"get": {"tags": ["User URL"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch URL của người dùng", "description": "<PERSON><PERSON><PERSON> danh sách URL thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọc theo tags, type và phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "keyword", "in": "query", "description": "Từ khóa tìm kiếm URL, tìm kiếm theo title, content và url", "required": false, "schema": {"type": "string", "example": "nestjs tutorial"}}, {"name": "type", "in": "query", "description": "Loại URL cần lọc", "required": false, "schema": {"type": "string", "example": "web"}}, {"name": "tags", "in": "query", "description": "<PERSON><PERSON><PERSON> thẻ cần lọc", "required": false, "schema": {"type": "string", "example": "nestjs,tutorial,programming"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["title", "createdAt", "updatedAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}, {"name": "ownedByEnum", "in": "query", "description": "Loại người sở hữu URL", "required": false, "schema": {"type": "string", "enum": ["ADMIN", "USER"], "example": "USER"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách URL thành công"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UrlSchema"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User URL"], "summary": "Tạo URL mới", "description": "Tạo một URL mới cho người dùng hiện tại", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUrlDto"}, "examples": {"basic": {"summary": "URL c<PERSON> bản", "value": {"url": "https://example.com/article", "title": "<PERSON><PERSON><PERSON> viết hay", "description": "<PERSON><PERSON>i dung bài viết...", "tags": ["article", "tutorial"]}}}}}}, "responses": {"201": {"description": "Tạo URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "URL đã đư<PERSON><PERSON> tạo thành công"}, "data": {"$ref": "#/components/schemas/UrlSchema"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data/url/{id}": {"get": {"tags": ["User URL"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết URL", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một URL cụ thể theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của URL", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin chi tiết URL thành công"}, "data": {"$ref": "#/components/schemas/UrlSchema"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User URL"], "summary": "<PERSON><PERSON><PERSON> nhật thông tin URL", "description": "Cập nhật thông tin của một URL cụ thể. Chỉ có thể cập nhật URL thuộc sở hữu của người dùng hiện tại.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của URL", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUrlDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t URL thành công"}, "data": {"$ref": "#/components/schemas/UrlSchema"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data/url/batch": {"delete": {"tags": ["User URL"], "summary": "Xóa nhiều URL", "description": "Xóa nhiều URL cùng lúc theo danh sách ID. Chỉ có thể xóa URL thuộc sở hữu của người dùng hiện tại.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUrlsDto"}, "examples": {"single": {"summary": "Xóa một URL", "value": {"ids": ["550e8400-e29b-41d4-a716-************"]}}, "multiple": {"summary": "Xóa nhiều URL", "value": {"ids": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]}}}}}}, "responses": {"200": {"description": "Xóa URL thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đã xóa thành công 2 URL"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/data/url/crawl/direct": {"post": {"tags": ["User URL"], "summary": "Crawl URL trực tiếp", "description": "Crawl URL ngay lập tức và trả về kết quả hoàn chỉnh. Không sử dụng queue, phù hợp cho crawl nhỏ.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DirectCrawlDto"}}}}, "responses": {"200": {"description": "Crawl hoàn thành thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Crawl hoàn thành thành công"}, "data": {"$ref": "#/components/schemas/DirectCrawlResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/knowledge-files": {"get": {"tags": ["User Knowledge Files"], "summary": "<PERSON><PERSON><PERSON> danh sách file tri thức của người dùng", "description": "<PERSON><PERSON><PERSON> danh sách file tri thức thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọ<PERSON> và phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "search", "in": "query", "description": "Tên file cần tìm kiếm", "required": false, "schema": {"type": "string", "example": "hướng dẫn"}}, {"name": "extensions", "in": "query", "description": "<PERSON><PERSON><PERSON> theo định dạng file (phân cách bằng dấu phẩy)", "required": false, "schema": {"type": "string", "example": "pdf,docx,txt"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "Số lượng kết quả trên một trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["name", "createdAt", "storage"], "example": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "example": "DESC"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái file", "required": false, "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DELETED"], "example": "APPROVED"}}, {"name": "isForSale", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái đăng bán (true: đang bán, false: không bán)", "required": false, "schema": {"type": "boolean", "example": true}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách file tri thức thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách file thành công."}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FileResponseDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User Knowledge Files"], "summary": "<PERSON><PERSON><PERSON><PERSON> file tri thức", "description": "Tạo nhiều file tri thức cùng lúc và trả về presigned URLs để upload file.\n\n**Quy trình tự động:**\n1. Gọi API này để tạo file records và nhận presigned URLs\n2. Upload file lên S3 sử dụng presigned URLs\n3. <PERSON><PERSON> thống tự động xử lý với RAG API sau khi upload (không cần confirm)\n\n**Tham số RAG:**\n- `chunkSize`: Kích thước chunk cho RAG processing (mặc định: 4000)\n- `chunkOverlap`: Độ chồng lấp giữa các chunk (mặc định: 100)\n\n**Lưu ý:**\n- Cần truyền MIME type (không phải URL) cho từng file\n- File sẽ tự động được xử lý với RAG API sau khi upload thành công\n- Status file sẽ tự động chuyển từ DRAFT sang APPROVED", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchCreateFilesDto"}, "examples": {"single": {"summary": "Tạo một file tri thức", "description": "Tạo một file tri thức với thông tin cơ bản", "value": {"files": [{"name": "<PERSON><PERSON><PERSON> li<PERSON>u hướng dẫn của sơn sói.pdf", "mime": "application/pdf", "storage": 1024000}], "chunkSize": 4000, "chunkOverlap": 100}}, "multiple": {"summary": "<PERSON><PERSON><PERSON> file tri thức", "description": "Tạo nhiều file tri thức cùng lúc với các loại file khác nhau", "value": {"files": [{"name": "<PERSON><PERSON><PERSON> li<PERSON>u hướng dẫn của sơn sói.pdf", "mime": "application/pdf", "storage": 1024000}, {"name": "Báo cáo tháng.docx", "mime": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "storage": 512000}, {"name": "<PERSON><PERSON> liệu cấu hình.json", "mime": "application/json", "storage": 256000}], "chunkSize": 6000, "chunkOverlap": 150}}}}}}, "responses": {"201": {"description": "Đã tạo files tri thức thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đã tạo files tri thức thành công."}, "data": {"$ref": "#/components/schemas/BatchCreateFilesResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/knowledge-files/batch": {"delete": {"tags": ["User Knowledge Files"], "summary": "<PERSON>óa nhiều file tri thức", "description": "Xóa nhiều file tri thức cùng lúc theo danh sách ID", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFilesDto"}, "examples": {"single": {"summary": "Xóa một file", "description": "Xóa một file duy nhất", "value": {"fileIds": ["a1b2c3d4-e5f6-7890-abcd-ef1234567890"]}}, "multiple": {"summary": "Xóa n<PERSON>ều file", "description": "Xóa nhiều file cùng lúc", "value": {"fileIds": ["a1b2c3d4-e5f6-7890-abcd-ef1234567890", "b2c3d4e5-f6a7-8901-bcde-f01234567890"]}}}}}}, "responses": {"200": {"description": "Xóa file thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đã xóa 2 file thành công."}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "deletedCount": {"type": "integer", "example": 2}, "failedItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}, "reason": {"type": "string", "example": "File đã bị xóa trư<PERSON>c đó"}}}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/knowledge-files/{id}/submit": {"put": {"tags": ["User Knowledge Files"], "summary": "Gửi file tri thức để du<PERSON>", "description": "Gửi file tri thức để admin duyệt. File phải ở trạng thái nháp mới có thể gửi duyệt.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của file c<PERSON><PERSON><PERSON><PERSON>", "required": true, "schema": {"type": "string", "example": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}}], "responses": {"200": {"description": "File đã đ<PERSON><PERSON><PERSON> g<PERSON>i du<PERSON>t thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "File đã đ<PERSON><PERSON><PERSON> g<PERSON>i du<PERSON>t thành công."}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "File đã đ<PERSON><PERSON><PERSON> g<PERSON>i du<PERSON>t thành công"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/statistics": {"get": {"tags": ["User Statistics"], "summary": "<PERSON><PERSON><PERSON> thống kê số lượng dữ liệu của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> thống kê số lượng media files, knowledge files và URLs của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê số lượng dữ liệu thành công"}, "data": {"$ref": "#/components/schemas/UserStatisticsResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/statistics/storage": {"get": {"tags": ["User Statistics"], "summary": "<PERSON><PERSON><PERSON> thống kê dung lượng dữ liệu của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> thống kê chi tiết về dung lượng lưu trữ của người dùng hiện tại theo từng loại dữ liệu", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê dung lượng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê dung lượng dữ liệu thành công"}, "data": {"$ref": "#/components/schemas/UserStorageResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/statistics/s3-storage": {"get": {"tags": ["User Statistics"], "summary": "<PERSON><PERSON><PERSON> thống kê dung lượng S3 của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> thông tin sử dụng dung lượng S3 của người dùng hiện tại bao gồm tổng kích thướ<PERSON>, số lượ<PERSON> file và danh sách chi tiết các file", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê dung lượng S3 thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê dung lượng S3 thành công"}, "data": {"$ref": "#/components/schemas/UserS3StorageResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"MediaDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của media", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên file media", "example": "My beautiful image"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về media", "example": "An image uploaded by user", "nullable": true}, "size": {"type": "integer", "description": "Dung lượng file (byte)", "example": 1048576}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Mảng tag phân loại media", "example": ["shoe", "white sneaker"]}, "type": {"type": "string", "description": "Type phân loại media", "example": "image/png"}, "status": {"type": "string", "enum": ["DRAFT", "APPROVED", "PENDING", "REJECTED"], "description": "Trạng thái c<PERSON>a media", "example": "APPROVED"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON> nh<PERSON> (timestamp)", "example": 1640995200000}}, "required": ["id", "name", "size", "tags", "type", "status", "createdAt", "updatedAt"]}, "MediaCreateDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên file media", "example": "My beautiful image"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về media", "example": "An image uploaded by user", "nullable": true}, "size": {"type": "integer", "description": "Dung lượng file (byte)", "example": 1048576, "minimum": 1}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Mảng tag phân loại media", "example": ["shoe", "white sneaker"]}, "type": {"type": "string", "description": "Type phân loại media (chỉ hỗ trợ image/, video/, audio/)", "example": "image/png"}}, "required": ["name", "size", "tags", "type"]}, "MediaPresignedUrlResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của media", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên file media", "example": "My beautiful image"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả về media", "example": "An image uploaded by user", "nullable": true}, "size": {"type": "integer", "description": "Dung lượng file (byte)", "example": 1048576}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Mảng tag phân loại media", "example": ["shoe", "white sneaker"]}, "type": {"type": "string", "description": "Type phân loại media", "example": "image/png"}, "storageKey": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>u trữ", "example": "media/images/user_123/2024/01/my_beautiful_image_1744857340088.png"}, "presignedUrl": {"type": "string", "description": "URL presigned để upload", "example": "https://s3.amazonaws.com/bucket/media/images/user_123/2024/01/my_beautiful_image_1744857340088.png?X-Amz-Algorithm=..."}, "expiresAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn (timestamp)", "example": 1744857940088}, "status": {"type": "string", "enum": ["DRAFT", "APPROVED", "PENDING", "REJECTED"], "description": "Trạng thái c<PERSON>a media", "example": "PENDING"}}, "required": ["id", "name", "size", "tags", "type", "storageKey", "presignedUrl", "expiresAt", "status"]}, "DeleteMediaDto": {"type": "object", "properties": {"mediaIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Danh sách ID của media cần xóa", "example": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"], "minItems": 1}}, "required": ["mediaIds"]}, "UrlSchema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của URL", "example": "123e4567-e89b-12d3-a456-************"}, "url": {"type": "string", "format": "uri", "description": "Đường dẫn URL", "example": "https://example.com/article/how-to-use-nestjs"}, "title": {"type": "string", "description": "Tiêu đề của tài nguyên URL", "example": "Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao"}, "description": {"type": "string", "description": "Nội dung về tài nguyên URL", "example": "<PERSON><PERSON><PERSON> viết này hướng dẫn cách sử dụng NestJS..."}, "type": {"type": "string", "description": "<PERSON><PERSON>i tài nguyên URL", "example": "web", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Các thẻ phân loại URL", "example": ["<PERSON><PERSON><PERSON>", "tutorial", "backend"], "nullable": true}, "ownedBy": {"type": "integer", "description": "ID người sở hữu", "example": 1}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true}, "ownedByEnum": {"type": "string", "enum": ["ADMIN", "USER"], "description": "Loại người sở hữu URL", "example": "USER", "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (timestamp)", "example": 1640995200000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON> nh<PERSON> (timestamp)", "example": 1640995200000}}, "required": ["id", "url", "title", "description", "ownedBy", "isActive", "createdAt", "updatedAt"]}, "CreateUrlDto": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "Đường dẫn URL", "example": "https://example.com/article/how-to-use-nestjs", "maxLength": 2048}, "title": {"type": "string", "description": "Tiêu đề của tài nguyên URL", "example": "Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao", "maxLength": 512}, "description": {"type": "string", "description": "Nội dung về tài nguyên URL", "example": "<PERSON><PERSON><PERSON> viết này hướng dẫn cách sử dụng NestJS..."}, "type": {"type": "string", "description": "<PERSON><PERSON>i tài nguyên URL", "example": "web", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Các thẻ phân loại URL", "example": ["<PERSON><PERSON><PERSON>", "tutorial", "backend"], "nullable": true}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true, "default": true}}, "required": ["url", "title", "description"]}, "UpdateUrlDto": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "Đường dẫn URL", "example": "https://example.com/article/how-to-use-nestjs-updated", "maxLength": 2048}, "title": {"type": "string", "description": "Tiêu đề của tài nguyên URL", "example": "Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao - <PERSON><PERSON><PERSON> nhật", "maxLength": 512}, "description": {"type": "string", "description": "Nội dung về tài nguyên URL", "example": "<PERSON><PERSON><PERSON> viết này hướng dẫn cách sử dụng NestJS... (đã cập nhật)"}, "type": {"type": "string", "description": "<PERSON><PERSON>i tài nguyên URL", "example": "web", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Các thẻ phân loại URL", "example": ["<PERSON><PERSON><PERSON>", "tutorial", "backend", "updated"], "nullable": true}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true}}}, "DeleteUrlsDto": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "<PERSON>h sách ID của các URL cần xóa", "example": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"], "minItems": 1}}, "required": ["ids"]}, "DirectCrawlDto": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "URL cần crawl", "example": "https://example.com"}, "depth": {"type": "integer", "description": "<PERSON><PERSON> sâu crawl", "example": 2, "minimum": 1, "maximum": 5}, "maxUrls": {"type": "integer", "description": "Số lượng URL tối đa", "example": 10, "minimum": 1, "maximum": 100}}, "required": ["url", "depth"]}, "DirectCrawlResponseDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["success", "partial", "failed"], "description": "Tr<PERSON><PERSON> thái crawl", "example": "success"}, "message": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> kết quả", "example": "Crawl hoàn thành thành công"}, "urlsProcessed": {"type": "integer", "description": "Số URL đã xử lý", "example": 10}, "urlsSaved": {"type": "integer", "description": "Số URL đã lưu", "example": 8}, "errors": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch lỗi (n<PERSON><PERSON> có)", "example": ["Timeout crawling https://example.com/page1"]}}, "required": ["status", "message", "urlsProcessed", "urlsSaved"]}, "FileResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của file", "example": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}, "name": {"type": "string", "description": "Tên file", "example": "<PERSON><PERSON><PERSON> li<PERSON>u hướng dẫn.pdf"}, "extension": {"type": "string", "description": "Phần mở rộng của file", "example": "pdf"}, "storage": {"type": "integer", "description": "Dung lượng file (bytes)", "example": 1024}, "ownerType": {"type": "string", "enum": ["USER", "ADMIN"], "description": "Loại chủ sở hữu file", "example": "USER"}, "viewUrl": {"type": "string", "description": "URL xem file", "example": "https://cdn.example.com/knowledge/documents/2023/05/file-123456.pdf"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> điểm tạo (Unix timestamp)", "example": 1629026400000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật (Unix timestamp)", "example": 1629026400000}}, "required": ["id", "name", "extension", "storage", "ownerType", "viewUrl", "createdAt", "updatedAt"]}, "BatchCreateFilesDto": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên file", "maxLength": 255, "example": "<PERSON><PERSON><PERSON> li<PERSON>u hướng dẫn của sơn sói.pdf"}, "mime": {"type": "string", "description": "Loại file (MIME type)", "maxLength": 100, "enum": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "application/json", "text/html", "text/plain"], "example": "application/pdf"}, "storage": {"type": "integer", "description": "Dung lượng file (bytes)", "minimum": 1, "example": 1024000}}, "required": ["name", "mime", "storage"]}, "description": "<PERSON><PERSON> s<PERSON>ch các file cần tạo", "minItems": 1}, "chunkSize": {"type": "integer", "description": "<PERSON><PERSON><PERSON> thước tùy chỉnh của mỗi đoạn tính bằng số ký tự (mặc định: 4000, tối ưu: 4000-8000)", "minimum": 100, "maximum": 10000, "default": 4000, "example": 4000}, "chunkOverlap": {"type": "integer", "description": "<PERSON><PERSON> chồng lấp tùy chỉnh giữa các đoạn tính bằng số ký tự (mặc định: 100, tối ưu: 100-200)", "minimum": 0, "maximum": 1000, "default": 100, "example": 100}}, "required": ["files"]}, "FileCreationInfoDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của file tri thức đã tạo", "example": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}, "name": {"type": "string", "description": "Tên file", "example": "<PERSON><PERSON><PERSON> li<PERSON>u hướng dẫn của sơn sói.pdf"}, "uploadUrl": {"type": "string", "description": "URL ký sẵn để tải file lên", "example": "https://storage.example.com/presigned-url?token=abc123"}, "storageKey": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> lưu trữ của file trên hệ thống", "example": "123/knowledge_files/document-123456-abcdef.pdf"}}, "required": ["id", "name", "uploadUrl", "storageKey"]}, "BatchCreateFilesResponseDto": {"type": "object", "properties": {"files": {"type": "array", "items": {"$ref": "#/components/schemas/FileCreationInfoDto"}, "description": "<PERSON><PERSON> sách thông tin các file đã tạo"}}, "required": ["files"]}, "DeleteFilesDto": {"type": "object", "properties": {"fileIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Danh sách ID file cần xóa", "example": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"], "minItems": 1}}, "required": ["fileIds"]}, "UserStatisticsResponseDto": {"type": "object", "properties": {"totalMediaFiles": {"type": "integer", "description": "Tổng số media files của người dùng", "example": 25}, "totalKnowledgeFiles": {"type": "integer", "description": "Tổng số knowledge files của người dùng", "example": 15}, "totalUrls": {"type": "integer", "description": "Tổng số URLs của người dùng", "example": 10}, "totalItems": {"type": "integer", "description": "Tổng số items", "example": 55}}, "required": ["totalMediaFiles", "totalKnowledgeFiles", "totalUrls", "totalItems"]}, "UserStorageResponseDto": {"type": "object", "properties": {"totalLimit": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> hạn tổng dung lượng (bytes)", "example": 1073741824}, "totalUsed": {"type": "integer", "description": "Tổng dung lượng đã sử dụng (bytes)", "example": 536870912}, "totalRemaining": {"type": "integer", "description": "Dung lượng còn lại (bytes)", "example": 536870912}, "usagePercentage": {"type": "number", "format": "float", "description": "<PERSON>ần tr<PERSON>m sử dụng dung lượng", "example": 50.0}, "mediaUsed": {"type": "integer", "description": "Dung lượng media đã sử dụng (bytes)", "example": 268435456}, "knowledgeFilesUsed": {"type": "integer", "description": "Dung lượng knowledge files đã sử dụng (bytes)", "example": 268435456}}, "required": ["totalLimit", "totalUsed", "totalRemaining", "usagePercentage", "mediaUsed", "knowledgeFilesUsed"]}, "UserS3StorageResponseDto": {"type": "object", "properties": {"totalSize": {"type": "integer", "description": "Tổng dung lượng S3 (bytes)", "example": 1073741824}, "totalFiles": {"type": "integer", "description": "Tổng số file trên S3", "example": 150}, "formattedSize": {"type": "string", "description": "Dung lượng đã format", "example": "1.00 GB"}, "files": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "Khóa file trên S3", "example": "123/media/images/2024/01/1704067200000-abc123.jpg"}, "size": {"type": "integer", "description": "Dung lượng file (bytes)", "example": 1048576}, "lastModified": {"type": "string", "format": "date-time", "description": "<PERSON>h<PERSON>i gian sửa đổi cuối", "example": "2024-01-01T00:00:00.000Z"}, "categoryFolder": {"type": "string", "description": "<PERSON><PERSON><PERSON> m<PERSON> phân lo<PERSON>i", "example": "images"}}, "required": ["key", "size", "lastModified", "categoryFolder"]}, "description": "<PERSON><PERSON> sách file trên S3"}}, "required": ["totalSize", "totalFiles", "formattedSize", "files"]}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "limit": {"type": "integer", "description": "Số lượng item mỗi trang", "example": 10}, "total": {"type": "integer", "description": "Tổng số item", "example": 100}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}}, "required": ["page", "limit", "total", "totalPages"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}