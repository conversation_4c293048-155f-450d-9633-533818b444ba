{"openapi": "3.0.0", "info": {"title": "Workflow API", "description": "API documentation for Workflow module", "version": "1.0.0"}, "servers": [{"url": "/api", "description": "API Server"}], "tags": [{"name": "User - Workflow", "description": "<PERSON><PERSON><PERSON><PERSON> lý workflow cho người dùng"}], "paths": {"/user/workflow": {"get": {"tags": ["User - Workflow"], "summary": "<PERSON><PERSON><PERSON> danh sách workflows", "description": "<PERSON><PERSON><PERSON> da<PERSON> sách workflows của user với pagination, search và filtering", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "S<PERSON> trang hiện tại (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượ<PERSON> bản ghi trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên workflow", "required": false, "schema": {"type": "string", "example": "My Workflow"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "required": false, "schema": {"type": "string", "enum": ["name", "createdAt", "updatedAt", "isActive"], "default": "updatedAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}, {"name": "isActive", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái k<PERSON>ch ho<PERSON>t", "required": false, "schema": {"type": "boolean", "example": true}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách workflows thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách workflows thành công"}, "result": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowListItemDto"}}, "total": {"type": "number", "example": 50}, "page": {"type": "number", "example": 1}, "limit": {"type": "number", "example": 10}, "totalPages": {"type": "number", "example": 5}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi khi lấy danh sách workflows"}}}, "post": {"tags": ["User - Workflow"], "summary": "Tạo workflow mới", "description": "Tạo một workflow mới cho user", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkflowDto"}}}}, "responses": {"201": {"description": "Tạo workflow thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "Tạo workflow thành công"}, "result": {"$ref": "#/components/schemas/WorkflowDetailDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu không hợp lệ hoặc tên workflow đã tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi khi tạo workflow"}}}}, "/user/workflow/search/{query}": {"get": {"tags": ["User - Workflow"], "summary": "T<PERSON><PERSON> kiếm workflows", "description": "<PERSON><PERSON><PERSON> k<PERSON>m workflows theo tên", "security": [{"bearerAuth": []}], "parameters": [{"name": "query", "in": "path", "description": "<PERSON>ừ khóa tìm kiếm", "required": true, "schema": {"type": "string", "example": "workflow"}}], "responses": {"200": {"description": "Tìm kiếm workflows thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Tìm kiếm workflows thành công"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowListItemDto"}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi khi tìm kiếm workflows"}}}}, "/user/workflow/statistics/overview": {"get": {"tags": ["User - Workflow"], "summary": "<PERSON><PERSON><PERSON> thống kê workflows", "description": "<PERSON><PERSON><PERSON> thống kê tổng quan về workflows của user", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê workflows thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê workflows thành công"}, "result": {"$ref": "#/components/schemas/WorkflowStatisticsDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi khi lấy thống kê workflows"}}}}, "/user/workflow/{id}": {"get": {"tags": ["User - Workflow"], "summary": "<PERSON><PERSON><PERSON> chi tiết workflow", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một workflow theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của workflow", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết workflow thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết workflow thành công"}, "result": {"$ref": "#/components/schemas/WorkflowDetailDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy workflow"}, "500": {"description": "Lỗi khi lấy chi tiết workflow"}}}, "patch": {"tags": ["User - Workflow"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t workflow", "description": "<PERSON><PERSON><PERSON> nhật thông tin của một workflow", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của workflow", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkflowDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật workflow thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật workflow thành công"}, "result": {"$ref": "#/components/schemas/WorkflowDetailDto"}}}}}}, "400": {"description": "<PERSON><PERSON> liệu không hợp lệ hoặc tên workflow đã tồn tại"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy workflow"}, "500": {"description": "Lỗi khi cập nhật workflow"}}}, "delete": {"tags": ["User - Workflow"], "summary": "Xóa workflow", "description": "Xóa một workflow theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của workflow", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Xóa workflow thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Xóa workflow thành công"}, "result": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy workflow"}, "500": {"description": "Lỗi khi xóa workflow"}}}}, "/user/workflow/{id}/status": {"patch": {"tags": ["User - Workflow"], "summary": "Toggle trạng thái workflow", "description": "Bật/tắt trạng thái kích hoạt của workflow", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của workflow", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToggleWorkflowStatusDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật trạng thái workflow thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật trạng thái workflow thành công"}, "result": {"$ref": "#/components/schemas/WorkflowDetailDto"}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy workflow"}, "500": {"description": "Lỗi khi cập nhật trạng thái workflow"}}}}, "/user/workflow-definitions/{id}/definition": {"put": {"tags": ["User - Workflow"], "summary": "Update workflow definition", "description": "Update the complete workflow definition including nodes, edges, and metadata", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Workflow ID", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkflowDefinitionDto"}}}}, "responses": {"200": {"description": "Workflow definition updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Workflow definition updated successfully"}, "result": {"type": "object", "properties": {"workflow": {"type": "object"}, "validation": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "object"}}, "warnings": {"type": "array", "items": {"type": "object"}}}}}}}}}}}, "400": {"description": "Invalid workflow definition or validation failed"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "Workflow not found"}, "500": {"description": "Lỗi khi cập nhật workflow definition"}}}}, "/user/workflow-definitions/{id}/definition/validate": {"post": {"tags": ["User - Workflow"], "summary": "Validate workflow definition", "description": "Validate workflow definition without saving changes", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "Workflow ID", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"nodes": {"type": "array"}, "edges": {"type": "array"}, "metadata": {"type": "object"}}}}}}, "responses": {"200": {"description": "Validation completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Workflow definition is valid"}, "result": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "object"}}, "warnings": {"type": "array", "items": {"type": "object"}}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "Workflow not found"}, "500": {"description": "Lỗi khi validate workflow definition"}}}}, "/user/workflow-executions": {"get": {"tags": ["User - Workflow"], "summary": "Get workflow executions", "description": "<PERSON><PERSON><PERSON> danh sách workflow executions với pagination và filter. Chỉ trả về executions của user hiệ<PERSON> tại.", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Số trang hiện tại", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượ<PERSON> bản ghi trên mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "required": false, "schema": {"type": "string", "default": "startedAt"}}, {"name": "sortOrder", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "Workflow executions retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Workflow executions retrieved successfully"}, "result": {"type": "object", "properties": {"executions": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowExecutionDto"}}, "total": {"type": "number", "example": 50}, "page": {"type": "number", "example": 1}, "limit": {"type": "number", "example": 20}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi khi lấy danh sách workflow executions"}}}, "post": {"tags": ["User - Workflow"], "summary": "Create new workflow execution", "description": "Tạo mới workflow execution với trigger data. Execution sẽ được queue và thực thi bởi worker.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkflowExecutionDto"}}}}, "responses": {"201": {"description": "Workflow execution created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "Workflow execution created successfully"}, "result": {"$ref": "#/components/schemas/WorkflowExecutionDto"}}}}}}, "400": {"description": "Invalid trigger data"}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "404": {"description": "Workflow not found"}, "500": {"description": "Lỗi khi tạo workflow execution"}}}}, "/user/workflow-nodes": {"get": {"tags": ["User - Workflow"], "summary": "<PERSON><PERSON><PERSON> danh sách node definitions cho user", "description": "<PERSON><PERSON><PERSON> danh sách node definitions có thể sử dụng trong workflow", "security": [{"bearerAuth": []}], "parameters": [{"name": "category", "in": "query", "description": "Lọc theo category", "required": false, "schema": {"type": "string", "example": "SYSTEM"}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> kiếm theo tên hoặc mô tả", "required": false, "schema": {"type": "string", "example": "start"}}], "responses": {"200": {"description": "Danh sách node definitions", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Node definitions retrieved successfully"}, "result": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/components/schemas/NodeDefinitionDto"}}, "total": {"type": "number", "example": 25}}}}}}}}, "401": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập"}, "500": {"description": "Lỗi khi lấy danh sách node definitions"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"WorkflowListItemDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của workflow", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên c<PERSON>a workflow", "example": "My Workflow"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true}, "createdAt": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1640995200000}, "updatedAt": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật cuối (Unix timestamp)", "example": 1640995200000}, "nodeCount": {"type": "number", "description": "Số lượng nodes trong workflow", "example": 5}, "edgeCount": {"type": "number", "description": "Số lượng edges trong workflow", "example": 4}}, "required": ["id", "name", "isActive", "createdAt", "updatedAt"]}, "WorkflowDetailDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của workflow", "example": "123e4567-e89b-12d3-a456-************"}, "userId": {"type": "number", "description": "ID của user sở hữu workflow", "example": 1}, "employeeId": {"type": "number", "description": "ID của employee (n<PERSON><PERSON> có)", "example": 1}, "name": {"type": "string", "description": "Tên c<PERSON>a workflow", "example": "My Workflow"}, "isActive": {"type": "boolean", "description": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "example": true}, "definition": {"$ref": "#/components/schemas/WorkflowDefinitionDto"}, "createdAt": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1640995200000}, "updatedAt": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật cuối (Unix timestamp)", "example": 1640995200000}}, "required": ["id", "userId", "name", "isActive", "definition", "createdAt", "updatedAt"]}, "WorkflowStatisticsDto": {"type": "object", "properties": {"totalWorkflows": {"type": "number", "description": "Tổng số workflows", "example": 10}, "activeWorkflows": {"type": "number", "description": "Số workflows đang hoạt động", "example": 7}, "inactiveWorkflows": {"type": "number", "description": "Số workflows đã tắt", "example": 3}}, "required": ["totalWorkflows", "activeWorkflows", "inactiveWorkflows"]}, "CreateWorkflowDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên của workflow (ph<PERSON>i unique cho mỗi user)", "example": "My First Workflow", "minLength": 1, "maxLength": 255}, "isActive": {"type": "boolean", "description": "Trạng thái kích ho<PERSON>t của workflow", "example": false, "default": false}, "definition": {"$ref": "#/components/schemas/WorkflowDefinitionDto"}}, "required": ["name"]}, "UpdateWorkflowDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên của workflow (ph<PERSON>i unique cho mỗi user)", "example": "Updated Workflow Name", "minLength": 1, "maxLength": 255}, "isActive": {"type": "boolean", "description": "Trạng thái kích ho<PERSON>t của workflow", "example": true}, "definition": {"$ref": "#/components/schemas/WorkflowDefinitionDto"}}}, "ToggleWorkflowStatusDto": {"type": "object", "properties": {"isActive": {"type": "boolean", "description": "Tr<PERSON>ng thái kích hoạt mới", "example": true}}, "required": ["isActive"]}, "WorkflowDefinitionDto": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowNodeDto"}, "description": "<PERSON>h sách nodes trong workflow", "example": [{"id": "start_node_1", "type": "system.start", "name": "Start Node", "position": {"x": 100, "y": 100}, "inputs": {}, "outputs": {}, "config": {}, "metadata": {}}]}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEdgeDto"}, "description": "<PERSON><PERSON> s<PERSON>ch edges kết n<PERSON>i các nodes", "example": [{"id": "edge_1", "sourceNodeId": "start_node_1", "targetNodeId": "end_node_1", "edgeType": "normal", "metadata": {}}]}, "metadata": {"$ref": "#/components/schemas/WorkflowDefinitionMetadataDto"}}, "required": ["nodes", "edges"]}, "WorkflowNodeDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the node within workflow", "example": "start_node_1", "maxLength": 255}, "type": {"type": "string", "description": "Node type following category.subcategory.action pattern", "example": "system.start"}, "name": {"type": "string", "description": "Human-readable name for the node", "example": "Start Node", "maxLength": 255}, "description": {"type": "string", "description": "Optional description of node functionality", "example": "This node starts the workflow execution", "maxLength": 1000}, "position": {"type": "object", "description": "Node position on canvas", "properties": {"x": {"type": "number", "example": 100}, "y": {"type": "number", "example": 200}}, "required": ["x", "y"]}, "size": {"type": "object", "description": "Node size dimensions", "properties": {"width": {"type": "number", "example": 200}, "height": {"type": "number", "example": 100}}}, "inputs": {"type": "object", "description": "Node input configuration", "example": {}}, "outputs": {"type": "object", "description": "Node output configuration", "example": {}}, "config": {"type": "object", "description": "Node-specific configuration", "example": {"timeout": 30000, "retries": 3}}, "metadata": {"type": "object", "description": "UI and display metadata", "example": {"color": "#FF5733", "icon": "play", "category": "system"}}}, "required": ["id", "type", "name", "position"]}, "WorkflowEdgeDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the edge within workflow", "example": "edge_1", "maxLength": 255}, "sourceNodeId": {"type": "string", "description": "ID of the source node", "example": "start_node_1"}, "sourcePort": {"type": "string", "description": "Output port of source node", "example": "success", "maxLength": 100}, "targetNodeId": {"type": "string", "description": "ID of the target node", "example": "action_node_1"}, "targetPort": {"type": "string", "description": "Input port of target node", "example": "input", "maxLength": 100}, "edgeType": {"type": "string", "description": "Type of edge connection", "example": "normal", "enum": ["normal", "conditional", "error", "success", "loop"]}, "condition": {"type": "object", "description": "Condition for conditional edges", "example": {"expression": "{{result}} === \"success\"", "operator": "equals", "value": "success"}}, "metadata": {"type": "object", "description": "UI and display metadata", "example": {"label": "Success Path", "color": "#00FF00", "style": "solid"}}}, "required": ["id", "sourceNodeId", "targetNodeId"]}, "WorkflowDefinitionMetadataDto": {"type": "object", "properties": {"version": {"type": "string", "description": "Semantic version of workflow definition", "example": "1.0.0"}, "schemaVersion": {"type": "string", "description": "Version of workflow schema used", "example": "1.0"}, "canvas": {"type": "object", "description": "Canvas display settings", "properties": {"viewport": {"type": "object", "properties": {"x": {"type": "number", "example": 0}, "y": {"type": "number", "example": 0}, "zoom": {"type": "number", "example": 1}}}, "grid": {"type": "object", "properties": {"enabled": {"type": "boolean", "example": true}, "size": {"type": "number", "example": 20}, "snapToGrid": {"type": "boolean", "example": true}}}}}, "variables": {"type": "object", "description": "Workflow-level variables", "example": {"apiKey": {"type": "string", "value": "", "description": "API key for external service"}}}, "settings": {"type": "object", "description": "Workflow execution settings", "example": {"timeout": 300000, "maxRetries": 3}}}}, "UpdateWorkflowDefinitionDto": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowNodeDto"}, "description": "Array of workflow nodes", "example": [{"id": "start_node_1", "type": "system.start", "name": "Start Node", "position": {"x": 100, "y": 200}, "inputs": {}, "outputs": {}}]}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEdgeDto"}, "description": "Array of workflow edges", "example": [{"id": "edge_1", "sourceNodeId": "start_node_1", "targetNodeId": "action_node_1", "edgeType": "normal"}]}, "metadata": {"$ref": "#/components/schemas/WorkflowDefinitionMetadataDto"}}, "required": ["nodes"]}, "WorkflowExecutionDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của workflow execution", "example": "123e4567-e89b-12d3-a456-************"}, "workflowId": {"type": "string", "description": "ID của workflow", "example": "123e4567-e89b-12d3-a456-************"}, "status": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái execution", "enum": ["QUEUED", "RUNNING", "COMPLETED", "FAILED", "CANCELLED", "PAUSED"], "example": "RUNNING"}, "triggerData": {"type": "object", "description": "<PERSON><PERSON> liệu trigger execution", "example": {}}, "result": {"type": "object", "description": "<PERSON><PERSON><PERSON> q<PERSON> execution", "example": {}}, "error": {"type": "object", "description": "Thông tin lỗi (n<PERSON>u có)", "example": null}, "startedAt": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu (Unix timestamp)", "example": 1640995200000}, "completedAt": {"type": "number", "description": "<PERSON><PERSON><PERSON><PERSON> gian hoàn thành (Unix timestamp)", "example": 1640995260000}, "metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> execution", "example": {"userId": 1, "source": "manual"}}}, "required": ["id", "workflowId", "status", "startedAt"]}, "CreateWorkflowExecutionDto": {"type": "object", "properties": {"workflowId": {"type": "string", "description": "ID của workflow cần thực thi", "example": "123e4567-e89b-12d3-a456-************"}, "triggerData": {"type": "object", "description": "<PERSON><PERSON> liệu trigger execution", "example": {"input": "test data"}}, "metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> b<PERSON> sung", "example": {"source": "manual", "description": "Test execution"}}}, "required": ["workflowId"]}, "NodeDefinitionDto": {"type": "object", "properties": {"id": {"type": "string", "description": "ID của node definition", "example": "123e4567-e89b-12d3-a456-************"}, "type": {"type": "string", "description": "Loại node", "example": "system.start"}, "name": {"type": "string", "description": "Tên node", "example": "Start Node"}, "description": {"type": "string", "description": "Mô tả node", "example": "Node bắt đầu workflow"}, "category": {"type": "string", "description": "Category của node", "example": "SYSTEM"}, "version": {"type": "string", "description": "Phiên bản node", "example": "1.0.0"}, "inputSchema": {"type": "object", "description": "Schema cho input của node", "example": {}}, "outputSchema": {"type": "object", "description": "Schema cho output của node", "example": {}}, "configSchema": {"type": "object", "description": "Schema cho config của node", "example": {}}, "metadata": {"type": "object", "description": "Metadata của node", "example": {"icon": "play", "color": "#00FF00"}}}, "required": ["id", "type", "name", "category", "version"]}}}}