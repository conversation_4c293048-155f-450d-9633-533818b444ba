# Dockerfile cho RedAI Workflow MCP Server
FROM python:3.11-slim

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements và cài đặt Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY config/ ./config/

# Thiết lập environment variables
ENV PYTHONPATH=/app
ENV REDAI_WORKFLOW_API_BASE_URL=https://api.redai.com
ENV WORKFLOW_HTTP_HOST=0.0.0.0
ENV WORKFLOW_HTTP_PORT=8012
ENV WORKFLOW_HTTP_PATH=/mcp
ENV WORKFLOW_TRANSPORT=streamable-http

# Expose port
EXPOSE 8012

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8012/mcp || exit 1

# Chạy server
CMD ["python", "src/server/redai_system/workflow/workflow_server.py"]
