# RedAI Workflow MCP Server

Server MCP cho RedAI Workflow Module API sử dụng FastMCP và OpenAPI Schema.

## 🚀 Tính năng

### **Workflow Management**
- ✅ **CRUD Operations**: Tạo, đọc, cập nhật, xóa workflows
- ✅ **Status Management**: Bật/tắt trạng thái workflow
- ✅ **Search**: Tìm kiếm workflows theo tên
- ✅ **Statistics**: Thống kê tổng quan workflows của user

### **Workflow Definition**
- ✅ **Definition Update**: Cập nhật workflow definition (nodes, edges, metadata)
- ✅ **Validation**: Validate workflow definition trước khi lưu
- ✅ **Node Management**: Quản lý nodes và edges trong workflow

### **Workflow Execution**
- ✅ **Execution Management**: Tạo và quản lý workflow executions
- ✅ **Trigger Data**: Hỗ trợ trigger data cho executions
- ✅ **Status Tracking**: Theo dõi trạng thái execution

### **Node Definitions**
- ✅ **Node Library**: Lấy danh sách node definitions có thể sử dụng
- ✅ **Category Filter**: Lọc nodes theo category
- ✅ **Search**: Tìm kiếm nodes theo tên hoặc mô tả

## 📋 API Endpoints

### **Workflow Operations**
- `GET /user/workflow` - Lấy danh sách workflows với pagination
- `POST /user/workflow` - Tạo workflow mới
- `GET /user/workflow/{id}` - Lấy chi tiết workflow
- `PATCH /user/workflow/{id}` - Cập nhật workflow
- `DELETE /user/workflow/{id}` - Xóa workflow
- `PATCH /user/workflow/{id}/status` - Toggle trạng thái workflow

### **Search & Statistics**
- `GET /user/workflow/search/{query}` - Tìm kiếm workflows
- `GET /user/workflow/statistics/overview` - Thống kê workflows

### **Workflow Definition**
- `PUT /user/workflow-definitions/{id}/definition` - Cập nhật definition
- `POST /user/workflow-definitions/{id}/definition/validate` - Validate definition

### **Workflow Execution**
- `GET /user/workflow-executions` - Lấy danh sách executions
- `POST /user/workflow-executions` - Tạo execution mới

### **Node Definitions**
- `GET /user/workflow-nodes` - Lấy danh sách node definitions

## 🔧 Cấu hình

### **Environment Variables**
```bash
# API Configuration
REDAI_WORKFLOW_API_BASE_URL=https://api.redai.com

# Server Configuration
WORKFLOW_HTTP_HOST=127.0.0.1
WORKFLOW_HTTP_PORT=8012
WORKFLOW_HTTP_PATH=/mcp
WORKFLOW_TRANSPORT=streamable-http
```

### **Transport Options**
- **Streamable HTTP** (mặc định): `http://127.0.0.1:8012/mcp`
- **SSE**: `http://127.0.0.1:8012/sse`
- **STDIO**: Command line interface

## 🚀 Khởi chạy

### **Cách 1: Python trực tiếp**
```bash
# Streamable HTTP (mặc định)
python src/server/redai_system/workflow/workflow_server.py

# SSE transport
python src/server/redai_system/workflow/workflow_server.py sse

# STDIO transport
python src/server/redai_system/workflow/workflow_server.py stdio
```

### **Cách 2: Docker**
```bash
# Build image
docker build -t redai-workflow-server src/server/redai_system/workflow/

# Chạy container
docker run -p 8012:8012 \
  -e REDAI_WORKFLOW_API_BASE_URL=https://api.redai.com \
  redai-workflow-server
```

### **Cách 3: Docker Compose**
```yaml
services:
  workflow-server:
    build: ./src/server/redai_system/workflow
    ports:
      - "8012:8012"
    environment:
      - REDAI_WORKFLOW_API_BASE_URL=https://api.redai.com
      - WORKFLOW_HTTP_HOST=0.0.0.0
```

## 🔐 Authentication

Server sử dụng **Bearer Token Authentication**:

- Client gửi Bearer token trong `Authorization` header khi connect
- Token được extract tự động và sử dụng cho tất cả API calls
- Không cần validation - token được pass-through trực tiếp đến API backend

### **Ví dụ kết nối**
```python
from fastmcp import FastMCPClient

# Kết nối với Bearer token
client = FastMCPClient(
    "http://localhost:8012/mcp",
    headers={"Authorization": "Bearer your_token_here"}
)
```

## 🛠️ Available Tools

### **Workflow Management Tools**
- `redai-workflow-get-user-workflows` - Lấy danh sách workflows
- `redai-workflow-create-user-workflow` - Tạo workflow mới
- `redai-workflow-get-workflow-detail` - Lấy chi tiết workflow
- `redai-workflow-update-workflow` - Cập nhật workflow
- `redai-workflow-delete-workflow` - Xóa workflow
- `redai-workflow-toggle-status` - Toggle trạng thái workflow

### **Search & Statistics Tools**
- `redai-workflow-search-workflows` - Tìm kiếm workflows
- `redai-workflow-get-statistics` - Lấy thống kê workflows

### **Definition Management Tools**
- `redai-workflow-update-definition` - Cập nhật workflow definition
- `redai-workflow-validate-definition` - Validate workflow definition

### **Execution Management Tools**
- `redai-workflow-get-executions` - Lấy danh sách executions
- `redai-workflow-create-execution` - Tạo execution mới

### **Node Definition Tools**
- `redai-workflow-get-node-definitions` - Lấy danh sách node definitions

## 📊 Monitoring

### **Health Check**
```bash
curl http://localhost:8012/mcp
```

### **Server Info**
Server tự động hiển thị thông tin chi tiết khi khởi động:
- API endpoints có sẵn
- Authentication configuration
- Transport options
- Available tools

## 🐛 Troubleshooting

### **Lỗi 401 Unauthorized**
- Kiểm tra Bearer token trong Authorization header
- Đảm bảo token chưa hết hạn
- Verify API endpoint yêu cầu authentication

### **Connection Issues**
- Kiểm tra port 8012 có available không
- Verify API_BASE_URL configuration
- Check network connectivity

### **Import Errors**
- Đảm bảo chạy từ đúng thư mục (repository root)
- Kiểm tra PYTHONPATH environment variable
- Verify tất cả dependencies đã được cài đặt

## 📚 Tài liệu tham khảo

- **FastMCP Documentation**: https://github.com/jlowin/fastmcp
- **MCP Protocol**: https://modelcontextprotocol.io/
- **OpenAPI Specification**: Xem `swagger.json` trong thư mục này
