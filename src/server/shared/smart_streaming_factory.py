"""
Common Streaming Factory cho API Tools

Factory này cung cấp common event flow cho tất cả API tools với khả năng inject
endpoint-specific data vào từng stage. Tất cả APIs sử dụng chung event flow
nhưng có thể customize data và messages.

Tính năng:
- Common event flow: started → progress → data → completed
- Endpoint-specific data injection cho từng stage
- Configurable progress steps và messages
- Flexible data payload cho từng endpoint type
- Universal streaming pattern với customization
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable, Tuple, Union
from enum import Enum

# Import streaming services
try:
    from .redis_stream_service import get_redis_stream_service
    from .streaming_tool_decorator import extract_stream_id_from_custom_header
except ImportError:
    # Fallback for testing
    async def get_redis_stream_service():
        return None

    def extract_stream_id_from_custom_header(header):
        return None


class CommonEventStage(Enum):
    """Common event stages cho tất cả APIs"""
    STARTED = "started"           # API call bắt đầu
    PREPARING = "preparing"       # Chuẩn bị request
    EXECUTING = "executing"       # Thực hiện API call
    PROCESSING = "processing"     # Xử lý response
    DATA_READY = "data_ready"     # Data đã sẵn sàng
    FINALIZING = "finalizing"     # Hoàn thiện kết quả
    COMPLETED = "completed"       # Hoàn thành
    ERROR = "error"              # Lỗi


class EndpointDataProvider:
    """
    Interface để provide endpoint-specific data cho từng stage
    """

    def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Lấy data cho stage cụ thể

        Args:
            stage: CommonEventStage enum
            context: Context data (path, method, params, etc.)

        Returns:
            Dict chứa data cho stage này
        """
        return {}

    def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
        """
        Lấy message cho stage cụ thể

        Args:
            stage: CommonEventStage enum
            context: Context data

        Returns:
            Message string cho stage này
        """
        return f"Đang xử lý {stage.value}..."

    def get_stage_progress(self, stage: CommonEventStage) -> int:
        """
        Lấy progress percentage cho stage

        Args:
            stage: CommonEventStage enum

        Returns:
            Progress percentage (0-100)
        """
        progress_map = {
            CommonEventStage.STARTED: 0,
            CommonEventStage.PREPARING: 15,
            CommonEventStage.EXECUTING: 40,
            CommonEventStage.PROCESSING: 70,
            CommonEventStage.DATA_READY: 85,
            CommonEventStage.FINALIZING: 95,
            CommonEventStage.COMPLETED: 100,
            CommonEventStage.ERROR: 100
        }
        return progress_map.get(stage, 50)


class CommonStreamingFactory:
    """
    Common Factory để tạo streaming-enabled API tools với unified event flow
    """

    def __init__(self, stream_key_header: str = "Stream-Key"):
        self.stream_key_header = stream_key_header
        self.default_stages = [
            CommonEventStage.STARTED,
            CommonEventStage.PREPARING,
            CommonEventStage.EXECUTING,
            CommonEventStage.PROCESSING,
            CommonEventStage.DATA_READY,
            CommonEventStage.FINALIZING,
            CommonEventStage.COMPLETED
        ]
        
    def create_endpoint_context(self, path: str, method: str, operation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Tạo context cho endpoint để sử dụng trong data providers

        Args:
            path: API path
            method: HTTP method
            operation: OpenAPI operation spec

        Returns:
            Dict chứa endpoint context
        """

        # Analyze endpoint characteristics
        parameters = operation.get("parameters", [])
        param_names = [p.get("name", "").lower() for p in parameters]

        has_pagination = any(name in param_names for name in ["page", "limit", "offset", "per_page"])
        has_search = any(name in param_names for name in ["search", "query", "filter", "q"])
        has_upload = "upload" in path.lower() or method.upper() == "POST"
        has_stats = any(keyword in path.lower() for keyword in ["statistics", "stats", "analytics"])

        return {
            "path": path,
            "method": method.upper(),
            "operation_id": operation.get("operationId", f"{method.lower()}_{path}"),
            "summary": operation.get("summary", ""),
            "description": operation.get("description", ""),
            "parameters": parameters,
            "param_names": param_names,
            "characteristics": {
                "has_pagination": has_pagination,
                "has_search": has_search,
                "has_upload": has_upload,
                "has_stats": has_stats,
                "is_list_endpoint": has_pagination or "list" in path.lower(),
                "is_single_item": "{id}" in path or not has_pagination,
                "complexity_score": len(param_names)
            }
        }
    
    def get_data_provider(self, context: Dict[str, Any]) -> EndpointDataProvider:
        """
        Lấy data provider phù hợp cho endpoint dựa trên context

        Args:
            context: Endpoint context

        Returns:
            EndpointDataProvider instance
        """

        characteristics = context.get("characteristics", {})

        # Chọn data provider dựa trên characteristics
        if characteristics.get("has_stats"):
            return StatisticsDataProvider()
        elif characteristics.get("has_pagination"):
            return PaginatedListDataProvider()
        elif characteristics.get("has_search"):
            return SearchDataProvider()
        elif characteristics.get("has_upload"):
            return UploadDataProvider()
        else:
            return DefaultDataProvider()


# Concrete Data Providers cho từng loại endpoint

class DefaultDataProvider(EndpointDataProvider):
    """Default data provider cho simple endpoints"""

    def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
        base_data = {
            "endpoint": f"{context.get('method', 'GET')} {context.get('path', '')}",
            "operation_id": context.get("operation_id", "unknown")
        }

        if stage == CommonEventStage.STARTED:
            return {**base_data, "stage": "initialization"}
        elif stage == CommonEventStage.PREPARING:
            return {**base_data, "stage": "request_preparation"}
        elif stage == CommonEventStage.EXECUTING:
            return {**base_data, "stage": "api_call"}
        elif stage == CommonEventStage.PROCESSING:
            return {**base_data, "stage": "response_processing"}
        elif stage == CommonEventStage.DATA_READY:
            return {**base_data, "stage": "data_available"}
        elif stage == CommonEventStage.FINALIZING:
            return {**base_data, "stage": "finalization"}
        elif stage == CommonEventStage.COMPLETED:
            return {**base_data, "stage": "completed", "status": "success"}

        return base_data

    def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
        messages = {
            CommonEventStage.STARTED: "Bắt đầu xử lý request...",
            CommonEventStage.PREPARING: "Đang chuẩn bị API call...",
            CommonEventStage.EXECUTING: "Đang thực hiện API call...",
            CommonEventStage.PROCESSING: "Đang xử lý response...",
            CommonEventStage.DATA_READY: "Dữ liệu đã sẵn sàng...",
            CommonEventStage.FINALIZING: "Đang hoàn thiện kết quả...",
            CommonEventStage.COMPLETED: "Hoàn thành thành công!"
        }
        return messages.get(stage, f"Đang xử lý {stage.value}...")


class PaginatedListDataProvider(EndpointDataProvider):
    """Data provider cho paginated list endpoints"""

    def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
        base_data = {
            "endpoint_type": "paginated_list",
            "path": context.get("path", ""),
            "method": context.get("method", "GET")
        }

        if stage == CommonEventStage.STARTED:
            return {**base_data, "list_type": "paginated", "estimated_pages": "calculating"}
        elif stage == CommonEventStage.PREPARING:
            return {**base_data, "pagination_params": {"page": 1, "limit": "default"}}
        elif stage == CommonEventStage.EXECUTING:
            return {**base_data, "fetching": "page_data", "current_page": 1}
        elif stage == CommonEventStage.PROCESSING:
            return {**base_data, "processing": "items", "items_count": "counting"}
        elif stage == CommonEventStage.DATA_READY:
            return {**base_data, "items_processed": "completed", "total_items": "calculated"}
        elif stage == CommonEventStage.FINALIZING:
            return {**base_data, "formatting": "list_response", "status": "ready"}
        elif stage == CommonEventStage.COMPLETED:
            return {**base_data, "list_ready": True, "status": "success"}

        return base_data

    def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
        messages = {
            CommonEventStage.STARTED: "Bắt đầu lấy danh sách...",
            CommonEventStage.PREPARING: "Đang chuẩn bị pagination parameters...",
            CommonEventStage.EXECUTING: "Đang fetch dữ liệu từ API...",
            CommonEventStage.PROCESSING: "Đang xử lý items trong danh sách...",
            CommonEventStage.DATA_READY: "Danh sách items đã sẵn sàng...",
            CommonEventStage.FINALIZING: "Đang format response danh sách...",
            CommonEventStage.COMPLETED: "Danh sách đã hoàn thành!"
        }
        return messages.get(stage, f"Đang xử lý danh sách - {stage.value}...")


class StatisticsDataProvider(EndpointDataProvider):
    """Data provider cho statistics endpoints"""

    def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
        base_data = {
            "endpoint_type": "statistics",
            "path": context.get("path", ""),
            "calculation_type": "analytics"
        }

        if stage == CommonEventStage.STARTED:
            return {**base_data, "metrics": ["count", "avg", "sum"], "data_range": "analyzing"}
        elif stage == CommonEventStage.PREPARING:
            return {**base_data, "data_sources": "identifying", "filters": "applying"}
        elif stage == CommonEventStage.EXECUTING:
            return {**base_data, "fetching": "raw_data", "records": "counting"}
        elif stage == CommonEventStage.PROCESSING:
            return {**base_data, "calculating": "metrics", "progress": "in_progress"}
        elif stage == CommonEventStage.DATA_READY:
            return {**base_data, "calculations": "completed", "metrics_ready": True}
        elif stage == CommonEventStage.FINALIZING:
            return {**base_data, "formatting": "statistics_report", "charts": "generating"}
        elif stage == CommonEventStage.COMPLETED:
            return {**base_data, "statistics_ready": True, "report": "available"}

        return base_data

    def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
        messages = {
            CommonEventStage.STARTED: "Bắt đầu tính toán thống kê...",
            CommonEventStage.PREPARING: "Đang chuẩn bị data sources...",
            CommonEventStage.EXECUTING: "Đang lấy dữ liệu thô...",
            CommonEventStage.PROCESSING: "Đang tính toán metrics...",
            CommonEventStage.DATA_READY: "Metrics đã được tính toán...",
            CommonEventStage.FINALIZING: "Đang tạo báo cáo thống kê...",
            CommonEventStage.COMPLETED: "Thống kê hoàn thành!"
        }
        return messages.get(stage, f"Đang tính toán - {stage.value}...")


class SearchDataProvider(EndpointDataProvider):
    """Data provider cho search endpoints"""

    def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
        base_data = {
            "endpoint_type": "search",
            "path": context.get("path", ""),
            "search_type": "advanced"
        }

        if stage == CommonEventStage.STARTED:
            return {**base_data, "query": "processing", "filters": "preparing"}
        elif stage == CommonEventStage.PREPARING:
            return {**base_data, "search_params": "optimizing", "indexes": "selecting"}
        elif stage == CommonEventStage.EXECUTING:
            return {**base_data, "searching": "database", "matching": "in_progress"}
        elif stage == CommonEventStage.PROCESSING:
            return {**base_data, "filtering": "results", "ranking": "by_relevance"}
        elif stage == CommonEventStage.DATA_READY:
            return {**base_data, "results": "filtered", "relevance": "calculated"}
        elif stage == CommonEventStage.FINALIZING:
            return {**base_data, "sorting": "results", "pagination": "applying"}
        elif stage == CommonEventStage.COMPLETED:
            return {**base_data, "search_completed": True, "results_ready": True}

        return base_data

    def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
        messages = {
            CommonEventStage.STARTED: "Bắt đầu tìm kiếm...",
            CommonEventStage.PREPARING: "Đang chuẩn bị search parameters...",
            CommonEventStage.EXECUTING: "Đang search trong database...",
            CommonEventStage.PROCESSING: "Đang filter và rank kết quả...",
            CommonEventStage.DATA_READY: "Kết quả search đã sẵn sàng...",
            CommonEventStage.FINALIZING: "Đang sort và format kết quả...",
            CommonEventStage.COMPLETED: "Tìm kiếm hoàn thành!"
        }
        return messages.get(stage, f"Đang tìm kiếm - {stage.value}...")


class UploadDataProvider(EndpointDataProvider):
    """Data provider cho upload endpoints"""

    def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
        base_data = {
            "endpoint_type": "upload",
            "path": context.get("path", ""),
            "upload_type": "file_processing"
        }

        if stage == CommonEventStage.STARTED:
            return {**base_data, "file": "preparing", "validation": "pending"}
        elif stage == CommonEventStage.PREPARING:
            return {**base_data, "file_check": "validating", "size": "calculating"}
        elif stage == CommonEventStage.EXECUTING:
            return {**base_data, "uploading": "in_progress", "bytes_sent": "counting"}
        elif stage == CommonEventStage.PROCESSING:
            return {**base_data, "processing": "file_content", "metadata": "extracting"}
        elif stage == CommonEventStage.DATA_READY:
            return {**base_data, "file_processed": True, "metadata": "ready"}
        elif stage == CommonEventStage.FINALIZING:
            return {**base_data, "storing": "file", "cleanup": "temporary_files"}
        elif stage == CommonEventStage.COMPLETED:
            return {**base_data, "upload_completed": True, "file_available": True}

        return base_data

    def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
        messages = {
            CommonEventStage.STARTED: "Bắt đầu upload file...",
            CommonEventStage.PREPARING: "Đang validate file...",
            CommonEventStage.EXECUTING: "Đang upload dữ liệu...",
            CommonEventStage.PROCESSING: "Đang xử lý file content...",
            CommonEventStage.DATA_READY: "File đã được xử lý...",
            CommonEventStage.FINALIZING: "Đang lưu file vào storage...",
            CommonEventStage.COMPLETED: "Upload hoàn thành!"
        }
        return messages.get(stage, f"Đang upload - {stage.value}...")


    def create_streaming_api_tool(
        self,
        path: str,
        method: str,
        operation: Dict[str, Any],
        base_url: str,
        original_api_function: Callable
    ) -> Callable:
        """
        Tạo streaming-enabled API tool
        
        Args:
            path: API path
            method: HTTP method
            operation: OpenAPI operation spec
            base_url: Base URL của API
            original_api_function: Function gốc để gọi API
            
        Returns:
            Enhanced function với streaming support
        """
        
        # Create endpoint context
        context = self.create_endpoint_context(path, method, operation)

        # Get appropriate data provider
        data_provider = self.get_data_provider(context)

        async def streaming_api_function(*args, **kwargs):
            """Enhanced API function với common streaming support"""

            # Extract stream key
            stream_id = extract_stream_id_from_custom_header(self.stream_key_header)

            # Nếu không có stream_id, chạy function gốc
            if not stream_id:
                return await original_api_function(*args, **kwargs)
            
            # Get Redis service
            try:
                redis_service = await get_redis_stream_service()
                if not redis_service:
                    return await original_api_function(*args, **kwargs)
            except Exception:
                return await original_api_function(*args, **kwargs)
            
            # Start streaming
            operation_id = operation.get("operationId", f"{method.lower()}_{path}")
            
            try:
                # Stream started với endpoint context
                start_data = data_provider.get_stage_data(CommonEventStage.STARTED, context)
                await redis_service.stream_tool_start(
                    stream_id=stream_id,
                    tool_name=operation_id,
                    arguments=start_data
                )

                # Execute với common streaming flow
                result = await self._execute_with_common_flow(
                    redis_service=redis_service,
                    stream_id=stream_id,
                    data_provider=data_provider,
                    context=context,
                    original_function=original_api_function,
                    args=args,
                    kwargs=kwargs
                )
                
                # Stream completion
                await redis_service.stream_tool_result(
                    stream_id=stream_id,
                    result=result,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # Stream error
                await redis_service.stream_tool_result(
                    stream_id=stream_id,
                    result=None,
                    success=False,
                    error_message=str(e)
                )
                raise
        
        return streaming_api_function

    async def _execute_with_common_flow(
        self,
        redis_service,
        stream_id: str,
        data_provider: EndpointDataProvider,
        context: Dict[str, Any],
        original_function: Callable,
        args: tuple,
        kwargs: dict
    ) -> Any:
        """
        Execute function với common streaming flow sử dụng data provider
        """

        # Common stages (excluding STARTED and COMPLETED)
        execution_stages = [
            CommonEventStage.PREPARING,
            CommonEventStage.EXECUTING,
            CommonEventStage.PROCESSING,
            CommonEventStage.DATA_READY,
            CommonEventStage.FINALIZING
        ]

        # Execute each stage với endpoint-specific data
        for stage in execution_stages:

            # Get stage-specific data và message
            stage_data = data_provider.get_stage_data(stage, context)
            stage_message = data_provider.get_stage_message(stage, context)
            stage_progress = data_provider.get_stage_progress(stage)

            # Stream progress
            await redis_service.stream_tool_progress(
                stream_id=stream_id,
                progress=stage_progress,
                message=stage_message
            )

            # Stream data chunk với endpoint-specific data
            await redis_service.stream_tool_data(
                stream_id=stream_id,
                data=stage_data,
                data_type=f"{stage.value}_data",
                message=stage_message
            )

            # Simulate processing time (có thể customize per stage)
            await asyncio.sleep(0.3)

            # Execute actual API call ở EXECUTING stage
            if stage == CommonEventStage.EXECUTING:
                result = await original_function(*args, **kwargs)

                # Stream intermediate result
                await redis_service.stream_tool_data(
                    stream_id=stream_id,
                    data={"api_response": "received", "processing": "starting"},
                    data_type="api_result",
                    message="API call hoàn thành, đang xử lý response..."
                )

        return result


    async def _execute_with_streaming(
        self,
        redis_service,
        stream_id: str,
        strategy: Dict[str, Any],
        endpoint_type: APIEndpointType,
        original_function: Callable,
        args: tuple,
        kwargs: dict
    ) -> Any:
        """
        Execute function với streaming progress theo strategy
        """
        
        events = strategy["events"]
        progress_steps = strategy["progress_steps"]
        messages = strategy["messages"]
        
        # Stream progress events
        for i, (event, progress, message) in enumerate(zip(events[:-1], progress_steps[:-1], messages[:-1])):
            
            if event == StreamingEventType.STARTED:
                # Already handled in main function
                continue
                
            elif event == StreamingEventType.FETCHING_DATA:
                await redis_service.stream_tool_progress(
                    stream_id=stream_id,
                    progress=progress,
                    message=message
                )
                await asyncio.sleep(0.3)  # Simulate API call time
                
            elif event == StreamingEventType.PROCESSING_PAGE:
                await redis_service.stream_tool_data(
                    stream_id=stream_id,
                    data={"processing_stage": "pagination", "current_page": 1},
                    data_type="pagination_info",
                    message=message
                )
                await asyncio.sleep(0.2)
                
            elif event == StreamingEventType.CALCULATING_STATS:
                await redis_service.stream_tool_data(
                    stream_id=stream_id,
                    data={"calculation_stage": "aggregating", "metrics": ["count", "avg", "sum"]},
                    data_type="calculation_info",
                    message=message
                )
                await asyncio.sleep(0.4)
                
            elif event in [StreamingEventType.SEARCHING, StreamingEventType.FILTERING, StreamingEventType.SORTING]:
                await redis_service.stream_tool_progress(
                    stream_id=stream_id,
                    progress=progress,
                    message=message
                )
                await asyncio.sleep(0.2)
                
            else:
                # Generic progress
                await redis_service.stream_tool_progress(
                    stream_id=stream_id,
                    progress=progress,
                    message=message
                )
                await asyncio.sleep(0.2)
        
        # Execute actual API call
        result = await original_function(*args, **kwargs)
        
        # Stream final progress
        final_progress = progress_steps[-1]
        final_message = messages[-1]
        
        await redis_service.stream_tool_progress(
            stream_id=stream_id,
            progress=final_progress,
            message=final_message
        )
        
        return result
