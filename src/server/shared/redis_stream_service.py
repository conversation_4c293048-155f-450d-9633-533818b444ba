"""
Redis Streaming Service cho MCP Tool Execution

Service này cung cấp khả năng stream kết quả tool execution lên Redis
trong khi vẫn trả về kết quả bình thường cho client.

Tính năng:
- Stream progress và intermediate results lên Redis
- Client-controlled stream_id từ HTTP headers
- Async streaming không block tool execution
- Automatic cleanup và expiry
- Error handling và fallback
"""

import json
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, Optional, List

import aioredis

# Cấu hình Redis
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
REDIS_STREAM_TTL = int(os.getenv("REDIS_STREAM_TTL", "3600"))  # 1 hour default


class RedisStreamService:
    """
    Service để stream tool execution results lên Redis
    
    Workflow:
    1. Client gửi request với X-Stream-Id header
    2. Tool execution bắt đầu, stream progress lên Redis
    3. <PERSON><PERSON> hoà<PERSON> thành, stream final result
    4. <PERSON><PERSON> c<PERSON> thể poll Redis để lấy real-time updates
    """

    def __init__(self, redis_url: str = REDIS_URL):
        self.redis_url = redis_url
        self.redis: Optional[aioredis.Redis] = None
        self._connection_pool = None

    async def connect(self):
        """Kết nối đến Redis"""
        if not self.redis:
            try:
                self._connection_pool = aioredis.ConnectionPool.from_url(
                    self.redis_url,
                    max_connections=20,
                    retry_on_timeout=True
                )
                self.redis = aioredis.Redis(connection_pool=self._connection_pool)

                # Test connection
                await self.redis.ping()
                print(f"✅ Kết nối Redis thành công: {self.redis_url}")

            except Exception as e:
                print(f"❌ Lỗi kết nối Redis: {e}")
                self.redis = None

    async def disconnect(self):
        """Đóng kết nối Redis"""
        if self.redis:
            await self.redis.close()
            self.redis = None
        if self._connection_pool:
            await self._connection_pool.disconnect()
            self._connection_pool = None

    @asynccontextmanager
    async def get_redis(self):
        """Context manager để đảm bảo Redis connection"""
        if not self.redis:
            await self.connect()

        if self.redis:
            yield self.redis
        else:
            # Fallback - không có Redis connection
            yield None

    async def stream_tool_chunk(
            self,
            stream_id: str,
            event_type: str,
            data: Dict[str, Any],
            chunk_id: Optional[str] = None
    ) -> bool:
        """
        Stream tool chunk với event name chuẩn

        Args:
            stream_id: ID của stream từ client header
            event_type: Loại event (started, progress, data, completed, error)
            data: Dữ liệu của chunk
            chunk_id: ID của chunk (optional, auto-generate nếu None)

        Returns:
            True nếu stream thành công, False nếu có lỗi
        """
        try:
            async with self.get_redis() as redis:
                if not redis:
                    return False

                # Chuẩn bị chunk data
                chunk_data = {
                    "event": "tool_chunk",
                    "event_type": event_type,
                    "timestamp": datetime.utcnow().isoformat(),
                    "chunk_id": chunk_id or f"chunk_{datetime.utcnow().timestamp()}"
                }

                # Merge với data được truyền vào
                for key, value in data.items():
                    if isinstance(value, (dict, list)):
                        chunk_data[key] = json.dumps(value, ensure_ascii=False)
                    else:
                        chunk_data[key] = str(value)

                # Add to Redis stream với event name
                await redis.xadd(
                    f"tool_stream:{stream_id}",
                    chunk_data,
                    maxlen=1000  # Giới hạn số lượng entries
                )

                # Set expiry cho stream
                await redis.expire(f"tool_stream:{stream_id}", REDIS_STREAM_TTL)

                return True

        except Exception as e:
            print(f"❌ Lỗi stream tool chunk: {e}")
            return False

    async def stream_tool_start(
            self,
            stream_id: str,
            tool_name: str,
            arguments: Dict[str, Any]
    ) -> bool:
        """
        Stream thông tin bắt đầu tool execution
        """
        return await self.stream_tool_chunk(
            stream_id=stream_id,
            event_type="started",
            data={
                "tool_name": tool_name,
                "arguments": arguments,
                "progress": 0,
                "status": "started"
            }
        )

    async def stream_tool_progress(
            self,
            stream_id: str,
            progress: int,
            message: str = "",
            data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Stream progress của tool execution
        """
        chunk_data = {
            "progress": min(100, max(0, progress)),
            "message": message,
            "status": "progress"
        }

        if data:
            chunk_data["data"] = data

        return await self.stream_tool_chunk(
            stream_id=stream_id,
            event_type="progress",
            data=chunk_data
        )

    async def stream_tool_data(
            self,
            stream_id: str,
            data: Dict[str, Any],
            data_type: str = "intermediate",
            message: str = ""
    ) -> bool:
        """
        Stream intermediate data từ tool execution

        Args:
            stream_id: ID của stream
            data: Dữ liệu cần stream
            data_type: Loại dữ liệu (intermediate, partial_result, etc.)
            message: Mô tả dữ liệu

        Returns:
            True nếu stream thành công
        """
        return await self.stream_tool_chunk(
            stream_id=stream_id,
            event_type="data",
            data={
                "data_type": data_type,
                "message": message,
                "payload": data,
                "status": "data"
            }
        )

    async def stream_tool_result(
            self,
            stream_id: str,
            result: Any,
            success: bool = True,
            error_message: str = ""
    ) -> bool:
        """
        Stream kết quả cuối cùng của tool execution
        """
        event_type = "completed" if success else "error"

        chunk_data = {
            "progress": 100,
            "success": success,
            "status": event_type
        }

        if success:
            # Serialize result based on type
            if isinstance(result, (str, int, float, bool)):
                chunk_data["result"] = str(result)
            elif isinstance(result, (dict, list)):
                chunk_data["result"] = result
            else:
                chunk_data["result"] = str(result)
        else:
            chunk_data["error"] = error_message

        return await self.stream_tool_chunk(
            stream_id=stream_id,
            event_type=event_type,
            data=chunk_data
        )

    async def get_stream_data(
            self,
            stream_id: str,
            start_id: str = "0",
            count: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Lấy dữ liệu từ stream (cho client polling)
        
        Args:
            stream_id: ID của stream
            start_id: ID bắt đầu đọc (0 = từ đầu, $ = mới nhất)
            count: Số lượng entries tối đa
            
        Returns:
            List các stream entries
        """
        try:
            async with self.get_redis() as redis:
                if not redis:
                    return []

                # Read from Redis stream
                streams = await redis.xread(
                    {f"tool_stream:{stream_id}": start_id},
                    count=count,
                    block=None  # Non-blocking read
                )

                result = []
                for stream_name, entries in streams:
                    for entry_id, fields in entries:
                        entry_data = {
                            "id": entry_id.decode() if isinstance(entry_id, bytes) else entry_id,
                            "stream_id": stream_id
                        }

                        # Convert fields to dict
                        for key, value in fields.items():
                            key_str = key.decode() if isinstance(key, bytes) else key
                            value_str = value.decode() if isinstance(value, bytes) else value
                            entry_data[key_str] = value_str

                        result.append(entry_data)

                return result

        except Exception as e:
            print(f"❌ Lỗi get stream data: {e}")
            return []

    async def cleanup_expired_streams(self, max_age_hours: int = 24):
        """
        Cleanup các streams cũ để tiết kiệm memory
        
        Args:
            max_age_hours: Tuổi tối đa của streams (giờ)
        """
        try:
            async with self.get_redis() as redis:
                if not redis:
                    return

                # Scan for tool_stream keys
                async for key in redis.scan_iter(match="tool_stream:*"):
                    try:
                        # Check if key exists and get TTL
                        ttl = await redis.ttl(key)
                        if ttl == -1:  # No expiry set
                            await redis.expire(key, REDIS_STREAM_TTL)
                        elif ttl == -2:  # Key doesn't exist
                            continue

                    except Exception as e:
                        print(f"❌ Lỗi cleanup stream {key}: {e}")

        except Exception as e:
            print(f"❌ Lỗi cleanup expired streams: {e}")


# Global instance
_redis_stream_service: Optional[RedisStreamService] = None


async def get_redis_stream_service() -> RedisStreamService:
    """Get global Redis stream service instance"""
    global _redis_stream_service

    if not _redis_stream_service:
        _redis_stream_service = RedisStreamService()
        await _redis_stream_service.connect()

    return _redis_stream_service


async def cleanup_redis_stream_service():
    """Cleanup global Redis stream service"""
    global _redis_stream_service

    if _redis_stream_service:
        await _redis_stream_service.disconnect()
        _redis_stream_service = None
