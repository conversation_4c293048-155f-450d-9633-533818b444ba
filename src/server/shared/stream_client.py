"""
Client utilities để poll Redis streams cho tool execution progress

Module này cung cấp utilities cho client để:
- Poll Redis streams để lấy real-time progress
- Handle streaming data từ tool execution
- Manage stream lifecycle

Usage:
    # Tạo stream client
    client = RedisStreamClient()
    
    # Poll stream với stream_id
    async for update in client.poll_stream("my-stream-id"):
        print(f"Progress: {update['progress']}%")
        if update['status'] == 'completed':
            break
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Union
import aioredis

from .redis_stream_service import REDIS_URL


class RedisStreamClient:
    """
    Client để poll Redis streams cho tool execution progress
    """
    
    def __init__(self, redis_url: str = REDIS_URL):
        self.redis_url = redis_url
        self.redis: Optional[aioredis.Redis] = None
        
    async def connect(self):
        """Kết nối đến Redis"""
        if not self.redis:
            try:
                self.redis = aioredis.from_url(
                    self.redis_url,
                    decode_responses=True,
                    retry_on_timeout=True
                )
                await self.redis.ping()
                print(f"✅ Stream client kết nối Redis: {self.redis_url}")
            except Exception as e:
                print(f"❌ Lỗi kết nối Redis: {e}")
                self.redis = None
                
    async def disconnect(self):
        """Đóng kết nối Redis"""
        if self.redis:
            await self.redis.close()
            self.redis = None
            
    async def poll_stream(
        self,
        stream_id: str,
        timeout: float = 30.0,
        poll_interval: float = 0.5
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Poll Redis stream để lấy updates
        
        Args:
            stream_id: ID của stream cần poll
            timeout: Timeout tổng cộng (seconds)
            poll_interval: Interval giữa các lần poll (seconds)
            
        Yields:
            Dict chứa stream update data
        """
        if not self.redis:
            await self.connect()
            
        if not self.redis:
            return
            
        stream_key = f"tool_stream:{stream_id}"
        last_id = "0"  # Start from beginning
        start_time = asyncio.get_event_loop().time()
        
        try:
            while True:
                # Check timeout
                elapsed = asyncio.get_event_loop().time() - start_time
                if elapsed > timeout:
                    yield {
                        "status": "timeout",
                        "message": f"Stream polling timeout sau {timeout}s",
                        "stream_id": stream_id
                    }
                    break
                
                # Read new entries from stream
                try:
                    streams = await self.redis.xread(
                        {stream_key: last_id},
                        count=10,
                        block=int(poll_interval * 1000)  # Convert to milliseconds
                    )
                    
                    if streams:
                        for stream_name, entries in streams:
                            for entry_id, fields in entries:
                                # Update last_id for next read
                                last_id = entry_id
                                
                                # Convert fields to proper types
                                update_data = {
                                    "id": entry_id,
                                    "stream_id": stream_id
                                }
                                
                                for key, value in fields.items():
                                    if key == "progress":
                                        try:
                                            update_data[key] = int(value)
                                        except:
                                            update_data[key] = 0
                                    elif key == "success":
                                        update_data[key] = value.lower() == "true"
                                    elif key in ["arguments", "data", "result", "payload"]:
                                        try:
                                            update_data[key] = json.loads(value)
                                        except:
                                            update_data[key] = value
                                    elif key == "event":
                                        update_data[key] = value  # tool_chunk event name
                                    elif key == "event_type":
                                        update_data[key] = value  # started, progress, data, completed, error
                                    elif key == "chunk_id":
                                        update_data[key] = value  # chunk identifier
                                    else:
                                        update_data[key] = value
                                
                                yield update_data

                                # Check if completed (support both old status and new event_type)
                                if (update_data.get("status") in ["completed", "error"] or
                                    update_data.get("event_type") in ["completed", "error"]):
                                    return
                    
                except asyncio.TimeoutError:
                    # No new data, continue polling
                    continue
                    
                except Exception as e:
                    yield {
                        "status": "error",
                        "message": f"Lỗi poll stream: {str(e)}",
                        "stream_id": stream_id
                    }
                    break
                    
        except Exception as e:
            yield {
                "status": "error", 
                "message": f"Lỗi poll stream: {str(e)}",
                "stream_id": stream_id
            }
            
    async def get_stream_history(
        self,
        stream_id: str,
        start_id: str = "0",
        count: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Lấy lịch sử stream data
        
        Args:
            stream_id: ID của stream
            start_id: ID bắt đầu đọc
            count: Số lượng entries tối đa
            
        Returns:
            List các stream entries
        """
        if not self.redis:
            await self.connect()
            
        if not self.redis:
            return []
            
        try:
            stream_key = f"tool_stream:{stream_id}"
            
            # Use XRANGE to get historical data
            entries = await self.redis.xrange(
                stream_key,
                min=start_id,
                max="+",
                count=count
            )
            
            result = []
            for entry_id, fields in entries:
                entry_data = {
                    "id": entry_id,
                    "stream_id": stream_id
                }
                
                for key, value in fields.items():
                    if key == "progress":
                        entry_data[key] = int(value)
                    elif key == "success":
                        entry_data[key] = value.lower() == "true"
                    elif key in ["arguments", "data", "result"]:
                        try:
                            entry_data[key] = json.loads(value)
                        except:
                            entry_data[key] = value
                    else:
                        entry_data[key] = value
                        
                result.append(entry_data)
                
            return result
            
        except Exception as e:
            print(f"❌ Lỗi get stream history: {e}")
            return []
            
    async def wait_for_completion(
        self,
        stream_id: str,
        timeout: float = 300.0
    ) -> Optional[Dict[str, Any]]:
        """
        Chờ tool completion và trả về kết quả cuối cùng
        
        Args:
            stream_id: ID của stream
            timeout: Timeout (seconds)
            
        Returns:
            Final result hoặc None nếu timeout/error
        """
        async for update in self.poll_stream(stream_id, timeout):
            if update.get("status") == "completed":
                return {
                    "success": True,
                    "result": update.get("result"),
                    "stream_id": stream_id
                }
            elif update.get("status") == "error":
                return {
                    "success": False,
                    "error": update.get("error", "Unknown error"),
                    "stream_id": stream_id
                }
            elif update.get("status") == "timeout":
                return {
                    "success": False,
                    "error": "Timeout waiting for completion",
                    "stream_id": stream_id
                }
                
        return None
        
    def generate_stream_id(self, prefix: str = "stream") -> str:
        """
        Generate unique stream ID
        
        Args:
            prefix: Prefix cho stream ID
            
        Returns:
            Unique stream ID
        """
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}_{timestamp}_{unique_id}"


# Utility functions

async def poll_tool_stream(
    stream_id: str,
    redis_url: str = REDIS_URL,
    timeout: float = 30.0
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    Convenience function để poll tool stream
    
    Args:
        stream_id: Stream ID
        redis_url: Redis URL
        timeout: Timeout
        
    Yields:
        Stream updates
    """
    client = RedisStreamClient(redis_url)
    try:
        async for update in client.poll_stream(stream_id, timeout):
            yield update
    finally:
        await client.disconnect()


async def wait_for_tool_completion(
    stream_id: str,
    redis_url: str = REDIS_URL,
    timeout: float = 300.0
) -> Optional[Dict[str, Any]]:
    """
    Convenience function để chờ tool completion
    
    Args:
        stream_id: Stream ID
        redis_url: Redis URL
        timeout: Timeout
        
    Returns:
        Final result
    """
    client = RedisStreamClient(redis_url)
    try:
        return await client.wait_for_completion(stream_id, timeout)
    finally:
        await client.disconnect()


# Example usage
async def example_usage():
    """Example cách sử dụng stream client"""
    
    # Generate stream ID
    client = RedisStreamClient()
    stream_id = client.generate_stream_id("my_tool")
    
    print(f"📡 Polling stream: {stream_id}")
    
    # Poll stream for updates
    async for update in client.poll_stream(stream_id, timeout=60.0):
        status = update.get("status", "unknown")
        progress = update.get("progress", 0)
        message = update.get("message", "")
        
        print(f"[{status}] {progress}% - {message}")
        
        if status in ["completed", "error"]:
            if status == "completed":
                result = update.get("result")
                print(f"✅ Tool completed: {result}")
            else:
                error = update.get("error", "Unknown error")
                print(f"❌ Tool failed: {error}")
            break
    
    await client.disconnect()


if __name__ == "__main__":
    asyncio.run(example_usage())
