"""
Configuration cho API Streaming Events

Module này định nghĩa các streaming configurations cho different types of API endpoints.
Mỗi loại API sẽ có events và progress patterns khác nhau.

Usage:
    from api_streaming_config import get_streaming_config
    
    config = get_streaming_config("list_paginated")
    events = config["events"]
    messages = config["messages"]
"""

from typing import Dict, List, Any
from enum import Enum


class APIStreamingPattern(Enum):
    """Patterns cho API streaming"""
    
    # Basic patterns
    SIMPLE_FETCH = "simple_fetch"           # Single API call
    PAGINATED_LIST = "paginated_list"       # List với pagination
    SEARCH_FILTER = "search_filter"         # Search với filtering
    STATISTICS_CALC = "statistics_calc"     # Statistics calculation
    
    # Complex patterns  
    MULTI_STEP_PROCESS = "multi_step_process"   # Multi-step processing
    FILE_UPLOAD = "file_upload"                 # File upload với processing
    BULK_OPERATION = "bulk_operation"           # Bulk operations
    REAL_TIME_SYNC = "real_time_sync"          # Real-time data sync


# Streaming configurations cho từng pattern
STREAMING_CONFIGS = {
    
    # Simple fetch pattern - cho single item APIs
    APIStreamingPattern.SIMPLE_FETCH: {
        "events": [
            {"type": "started", "progress": 0},
            {"type": "fetching_data", "progress": 30},
            {"type": "processing", "progress": 70},
            {"type": "completed", "progress": 100}
        ],
        "messages": [
            "Bắt đầu lấy dữ liệu...",
            "Đang gọi API endpoint...",
            "Đang xử lý response...",
            "Hoàn thành!"
        ],
        "timing": [0.1, 0.3, 0.2, 0.1],  # Seconds for each step
        "data_chunks": []
    },
    
    # Paginated list pattern - cho list APIs với pagination
    APIStreamingPattern.PAGINATED_LIST: {
        "events": [
            {"type": "started", "progress": 0},
            {"type": "fetching_data", "progress": 20},
            {"type": "processing_page", "progress": 50},
            {"type": "data_chunk", "progress": 70},
            {"type": "aggregating", "progress": 90},
            {"type": "completed", "progress": 100}
        ],
        "messages": [
            "Bắt đầu lấy danh sách...",
            "Đang fetch dữ liệu từ API...",
            "Đang xử lý trang dữ liệu...",
            "Đang tổng hợp items...",
            "Đang finalize kết quả...",
            "Danh sách đã sẵn sàng!"
        ],
        "timing": [0.1, 0.4, 0.3, 0.2, 0.2, 0.1],
        "data_chunks": [
            {"stage": "page_info", "data": {"current_page": 1, "total_pages": "calculating"}},
            {"stage": "items_count", "data": {"items_processed": 0, "estimated_total": "unknown"}}
        ]
    },
    
    # Search and filter pattern - cho search APIs
    APIStreamingPattern.SEARCH_FILTER: {
        "events": [
            {"type": "started", "progress": 0},
            {"type": "searching", "progress": 25},
            {"type": "filtering", "progress": 50},
            {"type": "sorting", "progress": 75},
            {"type": "data_chunk", "progress": 90},
            {"type": "completed", "progress": 100}
        ],
        "messages": [
            "Bắt đầu tìm kiếm...",
            "Đang search trong database...",
            "Đang filter theo tiêu chí...",
            "Đang sort kết quả...",
            "Đang format output...",
            "Tìm kiếm hoàn thành!"
        ],
        "timing": [0.1, 0.5, 0.3, 0.2, 0.2, 0.1],
        "data_chunks": [
            {"stage": "search_params", "data": {"query": "processing", "filters": "applied"}},
            {"stage": "results_count", "data": {"matches_found": "counting", "relevance": "calculating"}}
        ]
    },
    
    # Statistics calculation pattern - cho stats APIs
    APIStreamingPattern.STATISTICS_CALC: {
        "events": [
            {"type": "started", "progress": 0},
            {"type": "fetching_data", "progress": 20},
            {"type": "calculating_stats", "progress": 40},
            {"type": "data_chunk", "progress": 60},
            {"type": "aggregating", "progress": 80},
            {"type": "data_chunk", "progress": 95},
            {"type": "completed", "progress": 100}
        ],
        "messages": [
            "Bắt đầu tính toán thống kê...",
            "Đang lấy dữ liệu thô...",
            "Đang tính toán metrics...",
            "Đang tạo intermediate results...",
            "Đang tổng hợp báo cáo...",
            "Đang finalize statistics...",
            "Thống kê hoàn thành!"
        ],
        "timing": [0.1, 0.4, 0.6, 0.2, 0.4, 0.2, 0.1],
        "data_chunks": [
            {"stage": "raw_data", "data": {"records_count": "counting", "data_range": "analyzing"}},
            {"stage": "metrics", "data": {"avg": "calculating", "sum": "calculating", "count": "done"}},
            {"stage": "final_stats", "data": {"summary": "ready", "details": "formatted"}}
        ]
    },
    
    # Multi-step process pattern - cho complex APIs
    APIStreamingPattern.MULTI_STEP_PROCESS: {
        "events": [
            {"type": "started", "progress": 0},
            {"type": "validating", "progress": 15},
            {"type": "processing", "progress": 30},
            {"type": "data_chunk", "progress": 45},
            {"type": "transforming", "progress": 60},
            {"type": "data_chunk", "progress": 75},
            {"type": "finalizing", "progress": 90},
            {"type": "completed", "progress": 100}
        ],
        "messages": [
            "Bắt đầu xử lý phức tạp...",
            "Đang validate input...",
            "Đang xử lý bước 1...",
            "Kết quả bước 1 sẵn sàng...",
            "Đang transform dữ liệu...",
            "Kết quả transform sẵn sàng...",
            "Đang finalize process...",
            "Xử lý hoàn thành!"
        ],
        "timing": [0.1, 0.3, 0.5, 0.2, 0.4, 0.2, 0.3, 0.1],
        "data_chunks": [
            {"stage": "validation", "data": {"status": "passed", "warnings": []}},
            {"stage": "step1_result", "data": {"processed_items": "counting", "errors": []}},
            {"stage": "transform_result", "data": {"transformed": "done", "format": "json"}}
        ]
    },
    
    # File upload pattern - cho upload APIs
    APIStreamingPattern.FILE_UPLOAD: {
        "events": [
            {"type": "started", "progress": 0},
            {"type": "uploading", "progress": 30},
            {"type": "validating", "progress": 50},
            {"type": "processing", "progress": 70},
            {"type": "data_chunk", "progress": 85},
            {"type": "storing", "progress": 95},
            {"type": "completed", "progress": 100}
        ],
        "messages": [
            "Bắt đầu upload file...",
            "Đang upload dữ liệu...",
            "Đang validate file...",
            "Đang xử lý nội dung...",
            "File đã được xử lý...",
            "Đang lưu vào storage...",
            "Upload hoàn thành!"
        ],
        "timing": [0.1, 1.0, 0.3, 0.8, 0.2, 0.4, 0.1],
        "data_chunks": [
            {"stage": "upload_info", "data": {"size": "calculating", "type": "detected"}},
            {"stage": "processing_result", "data": {"status": "success", "metadata": "extracted"}}
        ]
    }
}


def get_streaming_config(pattern: APIStreamingPattern) -> Dict[str, Any]:
    """
    Lấy streaming configuration cho pattern
    
    Args:
        pattern: APIStreamingPattern enum
        
    Returns:
        Dict chứa streaming configuration
    """
    return STREAMING_CONFIGS.get(pattern, STREAMING_CONFIGS[APIStreamingPattern.SIMPLE_FETCH])


def detect_api_pattern(path: str, method: str, operation: Dict[str, Any]) -> APIStreamingPattern:
    """
    Tự động detect streaming pattern cho API endpoint
    
    Args:
        path: API path
        method: HTTP method
        operation: OpenAPI operation spec
        
    Returns:
        APIStreamingPattern enum
    """
    
    # Analyze path
    path_lower = path.lower()
    method_upper = method.upper()
    
    # Check for specific patterns in path
    if "upload" in path_lower or "file" in path_lower:
        return APIStreamingPattern.FILE_UPLOAD
    
    if "bulk" in path_lower or "batch" in path_lower:
        return APIStreamingPattern.BULK_OPERATION
    
    if "statistics" in path_lower or "stats" in path_lower or "analytics" in path_lower:
        return APIStreamingPattern.STATISTICS_CALC
    
    if "search" in path_lower:
        return APIStreamingPattern.SEARCH_FILTER
    
    # Analyze parameters
    parameters = operation.get("parameters", [])
    param_names = [p.get("name", "").lower() for p in parameters]
    
    # Check for pagination
    has_pagination = any(name in param_names for name in ["page", "limit", "offset", "per_page"])
    
    # Check for search/filter
    has_search = any(name in param_names for name in ["search", "query", "filter", "q"])
    
    # Check for complex processing
    has_complex_params = len(param_names) > 5
    
    # Decision logic
    if method_upper == "POST":
        if has_complex_params:
            return APIStreamingPattern.MULTI_STEP_PROCESS
        else:
            return APIStreamingPattern.SIMPLE_FETCH
    
    elif method_upper == "GET":
        if has_pagination:
            return APIStreamingPattern.PAGINATED_LIST
        elif has_search:
            return APIStreamingPattern.SEARCH_FILTER
        elif has_complex_params:
            return APIStreamingPattern.MULTI_STEP_PROCESS
        else:
            return APIStreamingPattern.SIMPLE_FETCH
    
    else:
        return APIStreamingPattern.SIMPLE_FETCH


def get_api_streaming_config(path: str, method: str, operation: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function để lấy streaming config cho API endpoint
    
    Args:
        path: API path
        method: HTTP method  
        operation: OpenAPI operation spec
        
    Returns:
        Dict chứa streaming configuration
    """
    pattern = detect_api_pattern(path, method, operation)
    config = get_streaming_config(pattern)
    
    # Add pattern info to config
    config["pattern"] = pattern.value
    config["detected_from"] = {
        "path": path,
        "method": method,
        "operation_id": operation.get("operationId", "unknown")
    }
    
    return config


# Example usage
if __name__ == "__main__":
    # Test pattern detection
    test_cases = [
        {
            "path": "/user/data/media",
            "method": "GET",
            "operation": {"parameters": [{"name": "page"}, {"name": "limit"}]}
        },
        {
            "path": "/user/data/statistics",
            "method": "GET", 
            "operation": {"parameters": [{"name": "date_range"}]}
        },
        {
            "path": "/user/data/search",
            "method": "GET",
            "operation": {"parameters": [{"name": "query"}, {"name": "filter"}]}
        }
    ]
    
    for case in test_cases:
        config = get_api_streaming_config(**case)
        print(f"Path: {case['path']}")
        print(f"Pattern: {config['pattern']}")
        print(f"Events: {len(config['events'])}")
        print(f"Messages: {config['messages'][0]}...")
        print("-" * 40)
