"""
Streaming Tool Decorator cho MCP Tools

Decorator này wrap các MCP tools để tự động stream progress và results lên Redis
trong khi vẫn trả về kết quả bình thường cho client.

Usage:
    @streaming_tool()
    async def my_tool(arg1: str, arg2: int) -> str:
        # Tool implementation
        return "result"
        
Client sử dụng:
    - Gửi header X-Stream-Id khi gọi tool
    - Poll Redis để lấy real-time updates
    - <PERSON><PERSON>ận kết quả cuối cùng từ MCP response bình thường
"""

import asyncio
import inspect
from functools import wraps
from typing import Any, Callable, Dict, Optional, TypeVar, Union
from fastmcp.server.dependencies import get_http_request

from .redis_stream_service import get_redis_stream_service

F = TypeVar('F', bound=Callable[..., Any])


def extract_stream_id_from_request() -> Optional[str]:
    """
    Extract stream_id từ HTTP request headers (default behavior)

    Client có thể gửi stream_id qua các headers sau:
    - Stream-Key (preferred - theo yêu cầu user)
    - X-Stream-Id (fallback)
    - x-stream-id (case insensitive)
    - Stream-Id

    Returns:
        stream_id nếu có, None nếu không có
    """
    return extract_stream_id_from_custom_header("Stream-Key")


def extract_stream_id_from_custom_header(header_name: str) -> Optional[str]:
    """
    Extract stream_id từ HTTP request headers với custom header name

    Args:
        header_name: Tên header chứa stream_id (ví dụ: "Stream-Key")

    Returns:
        stream_id nếu có, None nếu không có
    """
    try:
        request = get_http_request()
        if request:
            # Thử header name chính và các variations
            header_variations = [
                header_name,
                header_name.lower(),
                header_name.upper(),
                # Fallback headers
                "Stream-Key", "stream-key",
                "X-Stream-Id", "x-stream-id",
                "Stream-Id", "stream-id"
            ]

            # Remove duplicates while preserving order
            seen = set()
            unique_headers = []
            for h in header_variations:
                if h not in seen:
                    seen.add(h)
                    unique_headers.append(h)

            for header in unique_headers:
                stream_id = request.headers.get(header)
                if stream_id:
                    return stream_id.strip()

    except Exception as e:
        print(f"❌ Lỗi extract stream_id từ header '{header_name}': {e}")

    return None


def streaming_tool(
    enable_progress: bool = True,
    progress_interval: float = 1.0,
    auto_progress: bool = False,
    stream_key_header: str = "Stream-Key"
):
    """
    Decorator để enable streaming cho MCP tools

    Args:
        enable_progress: Có enable progress streaming không
        progress_interval: Interval giữa các progress updates (seconds)
        auto_progress: Tự động tạo progress updates dựa trên thời gian
        stream_key_header: Tên header chứa stream key (default: "Stream-Key")

    Usage:
        @streaming_tool()
        async def my_tool(arg1: str) -> str:
            return "result"

        @streaming_tool(auto_progress=True, progress_interval=0.5)
        async def long_running_tool(data: dict) -> dict:
            # Long running operation
            await asyncio.sleep(5)
            return {"status": "completed"}

        @streaming_tool(stream_key_header="Custom-Stream-Header")
        async def custom_header_tool(data: dict) -> dict:
            return {"result": "done"}
    """
    
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract stream_id từ request headers với custom header name
            stream_id = extract_stream_id_from_custom_header(stream_key_header)
            
            # Nếu không có stream_id, chạy tool bình thường
            if not stream_id:
                return await func(*args, **kwargs)
            
            # Get Redis stream service
            try:
                redis_service = await get_redis_stream_service()
            except Exception as e:
                print(f"❌ Không thể kết nối Redis, chạy tool bình thường: {e}")
                return await func(*args, **kwargs)
            
            # Extract function info
            func_name = func.__name__
            
            # Prepare arguments for logging (exclude sensitive data)
            safe_kwargs = {}
            for key, value in kwargs.items():
                if key.lower() in ['password', 'token', 'secret', 'key', 'auth']:
                    safe_kwargs[key] = "***"
                elif isinstance(value, (str, int, float, bool)):
                    safe_kwargs[key] = value
                elif isinstance(value, (dict, list)):
                    safe_kwargs[key] = f"<{type(value).__name__} with {len(value)} items>"
                else:
                    safe_kwargs[key] = f"<{type(value).__name__}>"
            
            # Stream tool start
            await redis_service.stream_tool_start(
                stream_id=stream_id,
                tool_name=func_name,
                arguments=safe_kwargs
            )
            
            # Auto progress task
            auto_progress_task = None
            if auto_progress and enable_progress:
                auto_progress_task = asyncio.create_task(
                    _auto_progress_updater(redis_service, stream_id, progress_interval)
                )
            
            try:
                # Execute original function
                if auto_progress:
                    # Run with auto progress
                    result = await func(*args, **kwargs)
                else:
                    # Run normally, tool có thể tự stream progress
                    # Inject stream_id vào kwargs nếu function accept nó
                    sig = inspect.signature(func)
                    if 'stream_id' in sig.parameters:
                        kwargs['stream_id'] = stream_id
                    if 'redis_service' in sig.parameters:
                        kwargs['redis_service'] = redis_service
                        
                    result = await func(*args, **kwargs)
                
                # Cancel auto progress if running
                if auto_progress_task and not auto_progress_task.done():
                    auto_progress_task.cancel()
                    try:
                        await auto_progress_task
                    except asyncio.CancelledError:
                        pass
                
                # Stream final result
                await redis_service.stream_tool_result(
                    stream_id=stream_id,
                    result=result,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # Cancel auto progress if running
                if auto_progress_task and not auto_progress_task.done():
                    auto_progress_task.cancel()
                    try:
                        await auto_progress_task
                    except asyncio.CancelledError:
                        pass
                
                # Stream error
                await redis_service.stream_tool_result(
                    stream_id=stream_id,
                    result=None,
                    success=False,
                    error_message=str(e)
                )
                
                # Re-raise exception
                raise
        
        return wrapper
    
    return decorator


async def _auto_progress_updater(
    redis_service, 
    stream_id: str, 
    interval: float,
    max_duration: float = 300.0  # 5 minutes max
):
    """
    Tự động update progress dựa trên thời gian
    
    Args:
        redis_service: Redis stream service
        stream_id: Stream ID
        interval: Update interval in seconds
        max_duration: Maximum duration to run auto progress
    """
    start_time = asyncio.get_event_loop().time()
    progress = 10  # Start at 10%
    
    try:
        while True:
            await asyncio.sleep(interval)
            
            elapsed = asyncio.get_event_loop().time() - start_time
            
            # Calculate progress (asymptotic approach to 90%)
            if elapsed < max_duration:
                # Progress grows slower as time passes
                progress = min(90, 10 + (80 * elapsed / max_duration))
            else:
                progress = 90  # Cap at 90%, final 10% for completion
            
            await redis_service.stream_tool_progress(
                stream_id=stream_id,
                progress=int(progress),
                message=f"Đang xử lý... ({elapsed:.1f}s)"
            )
            
            # Stop if we've been running too long
            if elapsed > max_duration:
                break
                
    except asyncio.CancelledError:
        # Task was cancelled, tool completed
        pass
    except Exception as e:
        print(f"❌ Lỗi auto progress updater: {e}")


# Utility functions for manual progress streaming

async def stream_progress(
    stream_id: Optional[str],
    progress: int,
    message: str = "",
    data: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Utility function để stream progress từ bên trong tool

    Args:
        stream_id: Stream ID (có thể None nếu không streaming)
        progress: Progress percentage (0-100)
        message: Progress message
        data: Additional data

    Returns:
        True if streamed successfully
    """
    if not stream_id:
        return False

    try:
        redis_service = await get_redis_stream_service()
        return await redis_service.stream_tool_progress(
            stream_id=stream_id,
            progress=progress,
            message=message,
            data=data
        )
    except Exception as e:
        print(f"❌ Lỗi stream progress: {e}")
        return False


async def stream_tool_data(
    stream_id: Optional[str],
    data: Dict[str, Any],
    data_type: str = "intermediate",
    message: str = ""
) -> bool:
    """
    Stream intermediate data từ bên trong tool (sử dụng tool_chunk event)

    Args:
        stream_id: Stream ID
        data: Dữ liệu cần stream
        data_type: Loại dữ liệu (intermediate, partial_result, etc.)
        message: Description message

    Returns:
        True if streamed successfully
    """
    if not stream_id:
        return False

    try:
        redis_service = await get_redis_stream_service()
        return await redis_service.stream_tool_data(
            stream_id=stream_id,
            data=data,
            data_type=data_type,
            message=message
        )
    except Exception as e:
        print(f"❌ Lỗi stream tool data: {e}")
        return False


async def stream_intermediate_result(
    stream_id: Optional[str],
    result: Any,
    message: str = "Kết quả trung gian"
) -> bool:
    """
    Stream intermediate result từ bên trong tool

    Args:
        stream_id: Stream ID
        result: Intermediate result
        message: Description message

    Returns:
        True if streamed successfully
    """
    if not stream_id:
        return False

    return await stream_tool_data(
        stream_id=stream_id,
        data={"intermediate_result": result},
        data_type="intermediate_result",
        message=message
    )
