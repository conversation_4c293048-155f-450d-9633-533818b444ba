"""
Endpoint Configuration Helper

Helper này giúp developers dễ dàng tạo custom data providers
cho các endpoints cụ thể mà không cần modify core factory.

Usage:
    # Tạo custom data provider
    provider = create_custom_provider(
        endpoint_type="custom_analytics",
        stage_messages={
            "preparing": "Đang chuẩn bị analytics...",
            "executing": "Đang chạy analytics engine...",
            "processing": "Đang phân tích kết quả..."
        },
        stage_data={
            "preparing": {"analytics_type": "advanced", "data_sources": 3},
            "executing": {"engine": "ml_model", "status": "running"},
            "processing": {"results": "analyzing", "confidence": 0.95}
        }
    )
    
    # Sử dụng trong factory
    factory.register_custom_provider("/analytics/advanced", provider)
"""

from typing import Dict, Any, Optional, Callable
from src.server.shared.smart_streaming_factory import (
    EndpointDataProvider, 
    CommonEventStage,
    CommonStreamingFactory
)


def create_custom_provider(
    endpoint_type: str,
    stage_messages: Optional[Dict[str, str]] = None,
    stage_data: Optional[Dict[str, Dict[str, Any]]] = None,
    stage_progress: Optional[Dict[str, int]] = None
) -> EndpointDataProvider:
    """
    Tạo custom data provider với configuration đơn giản
    
    Args:
        endpoint_type: Loại endpoint (để identify trong logs)
        stage_messages: Custom messages cho từng stage
        stage_data: Custom data cho từng stage
        stage_progress: Custom progress percentages cho từng stage
        
    Returns:
        CustomDataProvider instance
    """
    
    class CustomDataProvider(EndpointDataProvider):
        def __init__(self):
            self.endpoint_type = endpoint_type
            self.custom_messages = stage_messages or {}
            self.custom_data = stage_data or {}
            self.custom_progress = stage_progress or {}
            
        def get_stage_data(self, stage: CommonEventStage, context: Dict[str, Any]) -> Dict[str, Any]:
            base_data = {
                "endpoint_type": self.endpoint_type,
                "path": context.get("path", ""),
                "method": context.get("method", "GET"),
                "stage": stage.value
            }
            
            # Merge với custom data nếu có
            stage_key = stage.value
            if stage_key in self.custom_data:
                base_data.update(self.custom_data[stage_key])
            
            return base_data
        
        def get_stage_message(self, stage: CommonEventStage, context: Dict[str, Any]) -> str:
            stage_key = stage.value
            
            # Sử dụng custom message nếu có
            if stage_key in self.custom_messages:
                return self.custom_messages[stage_key]
            
            # Fallback to default messages
            default_messages = {
                "started": f"Bắt đầu {self.endpoint_type}...",
                "preparing": f"Đang chuẩn bị {self.endpoint_type}...",
                "executing": f"Đang thực hiện {self.endpoint_type}...",
                "processing": f"Đang xử lý kết quả {self.endpoint_type}...",
                "data_ready": f"Dữ liệu {self.endpoint_type} đã sẵn sàng...",
                "finalizing": f"Đang hoàn thiện {self.endpoint_type}...",
                "completed": f"{self.endpoint_type} hoàn thành!"
            }
            
            return default_messages.get(stage_key, f"Đang xử lý {stage_key}...")
        
        def get_stage_progress(self, stage: CommonEventStage) -> int:
            stage_key = stage.value
            
            # Sử dụng custom progress nếu có
            if stage_key in self.custom_progress:
                return self.custom_progress[stage_key]
            
            # Fallback to default progress
            return super().get_stage_progress(stage)
    
    return CustomDataProvider()


def create_simple_provider(
    endpoint_type: str,
    messages: list,
    data_templates: Optional[list] = None
) -> EndpointDataProvider:
    """
    Tạo simple provider với list of messages và data templates
    
    Args:
        endpoint_type: Loại endpoint
        messages: List messages cho từng stage (theo thứ tự stages)
        data_templates: List data templates cho từng stage
        
    Returns:
        SimpleDataProvider instance
    """
    
    stages = [
        CommonEventStage.STARTED,
        CommonEventStage.PREPARING, 
        CommonEventStage.EXECUTING,
        CommonEventStage.PROCESSING,
        CommonEventStage.DATA_READY,
        CommonEventStage.FINALIZING,
        CommonEventStage.COMPLETED
    ]
    
    # Map messages to stages
    stage_messages = {}
    for i, stage in enumerate(stages):
        if i < len(messages):
            stage_messages[stage.value] = messages[i]
    
    # Map data templates to stages
    stage_data = {}
    if data_templates:
        for i, stage in enumerate(stages):
            if i < len(data_templates) and data_templates[i]:
                stage_data[stage.value] = data_templates[i]
    
    return create_custom_provider(
        endpoint_type=endpoint_type,
        stage_messages=stage_messages,
        stage_data=stage_data
    )


class ExtendedCommonStreamingFactory(CommonStreamingFactory):
    """
    Extended factory với khả năng register custom providers
    """
    
    def __init__(self, stream_key_header: str = "Stream-Key"):
        super().__init__(stream_key_header)
        self.custom_providers = {}  # path -> provider mapping
        
    def register_custom_provider(self, path_pattern: str, provider: EndpointDataProvider):
        """
        Register custom provider cho specific path pattern
        
        Args:
            path_pattern: Path pattern (có thể dùng wildcards)
            provider: Custom data provider
        """
        self.custom_providers[path_pattern] = provider
        
    def get_data_provider(self, context: Dict[str, Any]) -> EndpointDataProvider:
        """
        Override để check custom providers trước
        """
        path = context.get("path", "")
        
        # Check exact match first
        if path in self.custom_providers:
            return self.custom_providers[path]
        
        # Check pattern matches
        for pattern, provider in self.custom_providers.items():
            if self._path_matches_pattern(path, pattern):
                return provider
        
        # Fallback to default logic
        return super().get_data_provider(context)
    
    def _path_matches_pattern(self, path: str, pattern: str) -> bool:
        """
        Simple pattern matching (có thể extend với regex)
        """
        if "*" in pattern:
            # Simple wildcard matching
            pattern_parts = pattern.split("*")
            if len(pattern_parts) == 2:
                prefix, suffix = pattern_parts
                return path.startswith(prefix) and path.endswith(suffix)
        
        return path == pattern


# Predefined providers cho common use cases

def create_analytics_provider() -> EndpointDataProvider:
    """Tạo provider cho analytics endpoints"""
    return create_custom_provider(
        endpoint_type="analytics",
        stage_messages={
            "preparing": "Đang chuẩn bị analytics engine...",
            "executing": "Đang chạy analytics algorithms...",
            "processing": "Đang phân tích patterns và trends...",
            "data_ready": "Analytics results đã sẵn sàng...",
            "finalizing": "Đang tạo analytics report..."
        },
        stage_data={
            "preparing": {"engine": "ml_analytics", "data_sources": "multiple"},
            "executing": {"algorithms": ["clustering", "regression"], "status": "running"},
            "processing": {"patterns_found": "analyzing", "trends": "calculating"},
            "data_ready": {"insights": "ready", "visualizations": "generating"},
            "finalizing": {"report_format": "json", "charts": "included"}
        }
    )


def create_export_provider() -> EndpointDataProvider:
    """Tạo provider cho export endpoints"""
    return create_custom_provider(
        endpoint_type="export",
        stage_messages={
            "preparing": "Đang chuẩn bị dữ liệu export...",
            "executing": "Đang thu thập dữ liệu...",
            "processing": "Đang format dữ liệu...",
            "data_ready": "Dữ liệu đã được format...",
            "finalizing": "Đang tạo file export..."
        },
        stage_data={
            "preparing": {"export_format": "xlsx", "data_range": "calculating"},
            "executing": {"records_collected": "counting", "filters": "applied"},
            "processing": {"formatting": "excel", "columns": "mapping"},
            "data_ready": {"rows": "ready", "size": "calculating"},
            "finalizing": {"file_generation": "in_progress", "compression": "applying"}
        }
    )


def create_import_provider() -> EndpointDataProvider:
    """Tạo provider cho import endpoints"""
    return create_custom_provider(
        endpoint_type="import",
        stage_messages={
            "preparing": "Đang validate file import...",
            "executing": "Đang đọc dữ liệu từ file...",
            "processing": "Đang validate và transform dữ liệu...",
            "data_ready": "Dữ liệu đã được validate...",
            "finalizing": "Đang lưu vào database..."
        },
        stage_data={
            "preparing": {"file_type": "detected", "size": "validating"},
            "executing": {"reading": "rows", "progress": "tracking"},
            "processing": {"validation": "rules", "errors": "checking"},
            "data_ready": {"valid_rows": "counted", "invalid_rows": "flagged"},
            "finalizing": {"saving": "database", "rollback": "prepared"}
        }
    )


# Example usage
if __name__ == "__main__":
    # Tạo extended factory
    factory = ExtendedCommonStreamingFactory()
    
    # Register custom providers
    factory.register_custom_provider("/analytics/*", create_analytics_provider())
    factory.register_custom_provider("/export/*", create_export_provider())
    factory.register_custom_provider("/import/*", create_import_provider())
    
    # Tạo custom provider cho specific endpoint
    custom_provider = create_simple_provider(
        endpoint_type="custom_process",
        messages=[
            "Bắt đầu custom process...",
            "Đang chuẩn bị custom logic...",
            "Đang execute custom algorithm...",
            "Đang xử lý custom results...",
            "Custom data đã sẵn sàng...",
            "Đang finalize custom output...",
            "Custom process hoàn thành!"
        ],
        data_templates=[
            {"process_type": "custom"},
            {"algorithm": "proprietary", "version": "2.0"},
            {"execution": "started", "threads": 4},
            {"results": "processing", "accuracy": 0.95},
            {"output": "ready", "format": "json"},
            {"cleanup": "in_progress"},
            {"status": "success", "duration": "calculated"}
        ]
    )
    
    factory.register_custom_provider("/custom/process", custom_provider)
    
    print("✅ Extended factory configured với custom providers!")
    print("📋 Registered patterns:")
    for pattern in factory.custom_providers.keys():
        print(f"   - {pattern}")
